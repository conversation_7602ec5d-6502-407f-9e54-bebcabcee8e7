#!/usr/bin/env python3
"""
Script de test pour la fonction recommendation_candidate_rh
"""

import requests
import json
import base64

# Configuration
BASE_URL = "http://localhost:5000"
CHAT_ENDPOINT = f"{BASE_URL}/api/chat/"

# Données de test - remplacez par un vrai candidat de votre base
TEST_CANDIDATE = {
    "id": "your-candidate-uuid-here",  # Remplacez par un UUID réel
    "role": "candidate"
}

def test_rh_recommendation_with_header():
    """Test avec authentification via header X-User"""
    print("=== Test RH avec header X-User ===")

    # Encoder les données utilisateur en base64
    user_data_json = json.dumps(TEST_CANDIDATE)
    user_data_b64 = base64.b64encode(user_data_json.encode('utf-8')).decode('utf-8')

    headers = {
        "Content-Type": "application/json",
        "X-User": user_data_b64
    }

    data = {
        "query": "Comment améliorer mon profil ?",
        "pageContext": "conseil"
    }

    try:
        response = requests.post(CHAT_ENDPOINT, headers=headers, json=data)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            response_data = response.json()
            print(f"Response preview: {response_data.get('response', '')[:200]}...")
            print("✅ Test réussi avec header X-User")
        else:
            print(f"❌ Test échoué avec header X-User: {response.json()}")

    except Exception as e:
        print(f"❌ Erreur lors du test avec header: {e}")

def test_rh_recommendation_with_body():
    """Test avec authentification via body"""
    print("\n=== Test RH avec body user ===")

    headers = {
        "Content-Type": "application/json"
    }

    data = {
        "query": "Quelles compétences dois-je développer ?",
        "pageContext": "conseil",
        "user": TEST_CANDIDATE
    }

    try:
        response = requests.post(CHAT_ENDPOINT, headers=headers, json=data)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            response_data = response.json()
            print(f"Response preview: {response_data.get('response', '')[:200]}...")
            print("✅ Test réussi avec body user")
        else:
            print(f"❌ Test échoué avec body user: {response.json()}")

    except Exception as e:
        print(f"❌ Erreur lors du test avec body: {e}")

def test_rh_recommendation_via_chat():
    """Test via l'endpoint chat principal avec une query RH"""
    print("\n=== Test via endpoint chat ===")
    
    # Encoder les données utilisateur en base64
    user_data_json = json.dumps(TEST_CANDIDATE)
    user_data_b64 = base64.b64encode(user_data_json.encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_data_b64
    }
    
    # Différentes queries de test
    test_queries = [
        "Comment améliorer mon profil ?",
        "Quelles compétences dois-je développer ?",
        "Conseil pour augmenter mon score de matching",
        "Aide moi à progresser dans ma carrière",
        "Recommandation RH pour mon profil"
    ]
    

    
    for query in test_queries:
        print(f"\n--- Test query: '{query}' ---")
        
        data = {
            "query": query,
            "pageContext": "conseil"
        }
        
        try:
            response = requests.post(CHAT_ENDPOINT, headers=headers, json=data)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"Response preview: {response_data.get('response', '')[:200]}...")
                print("✅ Test réussi")
            else:
                print(f"❌ Test échoué: {response.json()}")
                
        except Exception as e:
            print(f"❌ Erreur lors du test: {e}")

def test_unauthorized_access():
    """Test d'accès non autorisé"""
    print("\n=== Test accès non autorisé ===")

    headers = {
        "Content-Type": "application/json"
    }

    data = {
        "query": "Comment améliorer mon profil ?",
        "pageContext": "conseil"
    }

    try:
        response = requests.post(CHAT_ENDPOINT, headers=headers, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")

        if response.status_code == 401:
            print("✅ Test réussi - accès correctement refusé")
        else:
            print("❌ Test échoué - accès devrait être refusé")

    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")

def test_recruiter_access():
    """Test d'accès avec un recruteur (ne devrait pas déclencher les recommandations RH)"""
    print("\n=== Test accès recruteur ===")

    recruiter_data = {
        "id": "recruiter-uuid-here",
        "role": "recruiter"
    }

    user_data_json = json.dumps(recruiter_data)
    user_data_b64 = base64.b64encode(user_data_json.encode('utf-8')).decode('utf-8')

    headers = {
        "Content-Type": "application/json",
        "X-User": user_data_b64
    }

    data = {
        "query": "Comment améliorer mon profil ?",
        "pageContext": "conseil"
    }

    try:
        response = requests.post(CHAT_ENDPOINT, headers=headers, json=data)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            response_data = response.json()
            response_text = response_data.get('response', '')
            if "ANALYSE DE VOTRE PROFIL" in response_text:
                print("❌ Test échoué - recommandations RH ne devraient pas être déclenchées pour un recruteur")
            else:
                print("✅ Test réussi - recommandations RH non déclenchées pour recruteur")
                print(f"Response preview: {response_text[:200]}...")
        else:
            print(f"❌ Test échoué: {response.json()}")

    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")

if __name__ == "__main__":
    print("🧪 Tests de la fonction recommendation_candidate_rh")
    print("=" * 50)
    
    print("\n⚠️  IMPORTANT: Assurez-vous que:")
    print("1. Le serveur Flask est démarré (python app.py)")
    print("2. Vous avez remplacé 'your-candidate-uuid-here' par un UUID réel")
    print("3. Le candidat existe dans votre base de données")
    
    # Demander confirmation
    confirm = input("\nVoulez-vous continuer les tests ? (y/N): ")
    if confirm.lower() != 'y':
        print("Tests annulés.")
        exit()
    
    # Exécuter les tests
    test_unauthorized_access()
    test_recruiter_access()
    test_rh_recommendation_with_header()
    test_rh_recommendation_with_body()
    test_rh_recommendation_via_chat()
    
    print("\n" + "=" * 50)
    print("🏁 Tests terminés")
