#!/usr/bin/env python3
"""
Test de l'intégration OpenRouter LLM dans le chat
"""

import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

def test_llm_chat_integration():
    """Test l'intégration LLM avec différents types de requêtes"""
    
    url = "http://localhost:5000/chat/"
    headers = {"content-type": "application/json"}
    
    user_data = {
        "id": "63800628-55a2-4c33-97ce-5bcc5ee6a1bb",
        "role": "candidate"
    }
    
    # Tests avec différents types d'intentions
    test_cases = [
        {
            "name": "Analyse d'intention - Recommandations RH",
            "query": "Comment améliorer mon profil pour avoir plus d'opportunités ?",
            "pageContext": "conseil",
            "expected_service": "recommendation_rh"
        },
        {
            "name": "Analyse d'intention - Recherche d'emploi",
            "query": "développeur Python remote",
            "pageContext": "",
            "expected_service": "rag_search"
        },
        {
            "name": "Analyse d'intention - Recommandations d'offres",
            "query": "recommande moi des offres qui correspondent à mon profil",
            "pageContext": "",
            "expected_service": "recommendation_jobs"
        },
        {
            "name": "Analyse d'intention - Conseils généraux",
            "query": "comment préparer un entretien technique ?",
            "pageContext": "entretien",
            "expected_service": "general_advice"
        },
        {
            "name": "Génération LLM - Recherche avec résultats",
            "query": "ingénieur logiciel",
            "pageContext": "",
            "expected_service": "rag_search"
        }
    ]
    
    print("🚀 Test de l'intégration OpenRouter LLM")
    print("=" * 60)
    
    # Vérifier la configuration OpenRouter
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("⚠️  OPENROUTER_API_KEY non configurée - les tests utiliseront le fallback")
    else:
        print(f"✅ OPENROUTER_API_KEY configurée: {api_key[:10]}...")
    
    model = os.getenv("OPENROUTER_MODEL", "anthropic/claude-3.5-sonnet")
    print(f"🤖 Modèle configuré: {model}")
    print()
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"📋 Test {i}: {test_case['name']}")
        print(f"Query: '{test_case['query']}'")
        print(f"Context: '{test_case['pageContext']}'")
        print(f"Service attendu: {test_case['expected_service']}")
        
        body = {
            "query": test_case['query'],
            "pageContext": test_case['pageContext'],
            "role": "candidate",
            "user": user_data
        }
        
        try:
            response = requests.post(url, headers=headers, json=body, timeout=30)
            
            if response.status_code == 200:
                print("✅ SUCCESS")
                json_response = response.json()
                response_text = json_response.get('response', '')
                
                # Analyser la qualité de la réponse
                print(f"📝 Longueur de réponse: {len(response_text)} caractères")
                
                # Vérifier si la réponse contient des éléments attendus
                quality_indicators = {
                    "Salutation personnalisée": "Taoufiq" in response_text,
                    "Emojis utilisés": any(emoji in response_text for emoji in ["🏢", "📍", "💰", "🔧", "📝", "✅"]),
                    "Structure organisée": "**" in response_text,
                    "Appel à l'action": any(phrase in response_text.lower() for phrase in ["n'hésitez pas", "contactez", "postulez"]),
                    "Réponse en français": any(word in response_text.lower() for word in ["bonjour", "offres", "emploi", "entreprise"])
                }
                
                print("📊 Indicateurs de qualité:")
                for indicator, present in quality_indicators.items():
                    status = "✅" if present else "❌"
                    print(f"  {status} {indicator}")
                
                # Afficher un extrait de la réponse
                preview = response_text[:200].replace('\n', ' ')
                print(f"📄 Extrait: {preview}...")
                
                passed += 1
                
            else:
                print(f"❌ FAILED - Status: {response.status_code}")
                try:
                    error = response.json()
                    print(f"🚨 Error: {error}")
                except:
                    print(f"🚨 Raw Error: {response.text}")
                failed += 1
                
        except Exception as e:
            print(f"💥 Exception: {e}")
            failed += 1
        
        print("-" * 50)
        print()
    
    # Résumé
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    print(f"✅ Tests réussis: {passed}")
    print(f"❌ Tests échoués: {failed}")
    print(f"📈 Taux de réussite: {(passed/(passed+failed)*100):.1f}%")
    
    if api_key:
        print("\n🤖 INTÉGRATION LLM:")
        print("✅ OpenRouter configuré")
        print("✅ Analyse d'intention avec LLM")
        print("✅ Génération de réponses avec LLM")
    else:
        print("\n⚠️  INTÉGRATION LLM:")
        print("❌ OpenRouter non configuré (utilise le fallback)")
        print("✅ Analyse d'intention basique")
        print("✅ Génération de réponses simple")
    
    print("\n🎯 RECOMMANDATIONS:")
    if not api_key:
        print("1. Configurez OPENROUTER_API_KEY dans votre fichier .env")
        print("2. Obtenez une clé API sur https://openrouter.ai/")
        print("3. Redémarrez le serveur Flask")
    else:
        print("1. ✅ Configuration OpenRouter complète")
        print("2. ✅ Prêt pour la production")
    
    return passed, failed

def test_openrouter_direct():
    """Test direct de l'API OpenRouter"""
    
    print("\n🔧 Test direct de l'API OpenRouter")
    print("=" * 60)
    
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("❌ OPENROUTER_API_KEY non configurée - test ignoré")
        return
    
    url = "https://openrouter.ai/api/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "http://localhost:5000",
        "X-Title": "Job Matching Chatbot Test"
    }
    
    payload = {
        "model": os.getenv("OPENROUTER_MODEL", "anthropic/claude-3.5-sonnet"),
        "messages": [
            {
                "role": "system",
                "content": "Tu es un assistant IA pour un chatbot de recherche d'emploi. Réponds en français."
            },
            {
                "role": "user", 
                "content": "Analyse cette requête et détermine l'intention: 'développeur Python remote'"
            }
        ],
        "max_tokens": 200,
        "temperature": 0.3
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if 'choices' in data and len(data['choices']) > 0:
                content = data['choices'][0]['message']['content']
                print("✅ OpenRouter API fonctionne")
                print(f"📝 Réponse: {content}")
                print(f"🔧 Modèle utilisé: {payload['model']}")
            else:
                print(f"❌ Réponse invalide: {data}")
        else:
            print(f"❌ Erreur API: {response.status_code}")
            print(f"📝 Détails: {response.text}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")

def main():
    """Fonction principale"""
    print("🧪 Test complet de l'intégration OpenRouter LLM")
    print("Teste l'analyse d'intention et la génération de réponses avec LLM")
    
    # Test de l'intégration dans le chat
    passed, failed = test_llm_chat_integration()
    
    # Test direct de l'API OpenRouter
    test_openrouter_direct()
    
    print(f"\n✅ Tests terminés! ({passed} réussis, {failed} échoués)")

if __name__ == "__main__":
    main()
