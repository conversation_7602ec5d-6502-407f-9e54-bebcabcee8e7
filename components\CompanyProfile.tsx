'use client'

import { useState, useEffect } from 'react';
import { getCompanyByRecruiterId, updateCompanyByRecruiterId } from '@/services/companyService';

export default function CompanyProfile({ recruiterId }: { recruiterId: string }) {
  const [company, setCompany] = useState(null);
  const [loading, setLoading] = useState(true);

  // Récupérer les données de l'entreprise
  useEffect(() => {
    const fetchCompany = async () => {
      try {
        const data = await getCompanyByRecruiterId(recruiterId);
        setCompany(data);
      } catch (error) {
        console.error('Failed to fetch company:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCompany();
  }, [recruiterId]);

  // Mettre à jour l'entreprise
  const handleUpdateCompany = async (formData: any) => {
    try {
      const updatedCompany = await updateCompanyByRecruiterId(recruiterId, formData);
      setCompany(updatedCompany.data);
      console.log('Company updated successfully');
    } catch (error) {
      console.error('Failed to update company:', error);
    }
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      {/* Votre UI ici */}
    </div>
  );
}