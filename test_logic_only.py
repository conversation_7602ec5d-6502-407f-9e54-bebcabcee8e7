#!/usr/bin/env python3
"""
Test de la logique des fonctions RH sans serveur Flask
"""

import sys
import os

# Ajouter le répertoire racine au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_detection_logic():
    """Test de la logique de détection des intentions RH"""
    print("🔍 Test de la logique de détection...")
    
    try:
        from app.routes.chat import detect_rh_recommendation_intent
        
        # Test cases avec différentes queries
        test_cases = [
            # Cas positifs (doivent retourner True)
            ("Comment améliorer mon profil ?", True),
            ("Quelles compétences dois-je développer ?", True),
            ("Conseil pour augmenter mon score", True),
            ("Aide moi à progresser", True),
            ("Recommandation RH pour mon profil", True),
            ("Comment augmenter mes chances", True),
            ("Que dois-je apprendre", True),
            ("Conseil carrière", True),
            ("Amélioration compétences", True),
            ("Formation recommandée", True),
            
            # Cas négatifs (doivent retourner False)
            ("Recherche emploi Python", False),
            ("Salaire développeur", False),
            ("Offres d'emploi disponibles", False),
            ("Candidats pour ce poste", False),
            ("Informations entreprise", False),
            ("", False),
            ("Bonjour", False),
            ("Merci", False)
        ]
        
        passed = 0
        failed = 0
        
        for query, expected in test_cases:
            try:
                result = detect_rh_recommendation_intent(query)
                if result == expected:
                    print(f"✅ '{query}' -> {result}")
                    passed += 1
                else:
                    print(f"❌ '{query}' -> {result} (attendu: {expected})")
                    failed += 1
            except Exception as e:
                print(f"❌ Erreur avec '{query}': {e}")
                failed += 1
        
        print(f"\n📊 Résultats: {passed} réussis, {failed} échoués")
        return failed == 0
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_mock_recommendation():
    """Test de la logique de recommandation avec des données mockées"""
    print("\n🧪 Test de la logique de recommandation (mock)...")
    
    try:
        # Test de la structure de données que la fonction devrait traiter
        mock_candidate_skills = ['python', 'javascript', 'react', 'sql']
        mock_job_skills = [
            ['python', 'django', 'postgresql'],
            ['javascript', 'react', 'node.js'],
            ['python', 'machine learning', 'tensorflow'],
            ['java', 'spring', 'mysql'],
            ['javascript', 'vue.js', 'mongodb']
        ]
        
        # Simuler l'analyse des compétences
        all_job_skills = set()
        job_skills_frequency = {}
        
        for job_skills in mock_job_skills:
            for skill in job_skills:
                skill_clean = skill.lower().strip()
                all_job_skills.add(skill_clean)
                job_skills_frequency[skill_clean] = job_skills_frequency.get(skill_clean, 0) + 1
        
        # Identifier les compétences manquantes
        candidate_skills_set = set(skill.lower() for skill in mock_candidate_skills)
        missing_skills = all_job_skills - candidate_skills_set
        matching_skills = candidate_skills_set & all_job_skills
        
        # Trier par fréquence
        missing_skills_ranked = sorted(
            missing_skills, 
            key=lambda x: job_skills_frequency.get(x, 0), 
            reverse=True
        )
        
        matching_skills_ranked = sorted(
            matching_skills,
            key=lambda x: job_skills_frequency.get(x, 0),
            reverse=True
        )
        
        print(f"✅ Compétences candidat: {mock_candidate_skills}")
        print(f"✅ Compétences correspondantes: {list(matching_skills_ranked)}")
        print(f"✅ Compétences manquantes (top 5): {missing_skills_ranked[:5]}")
        print(f"✅ Fréquences calculées: {len(job_skills_frequency)} compétences uniques")
        
        # Vérifier que la logique fonctionne
        assert len(matching_skills) > 0, "Devrait avoir des compétences correspondantes"
        assert len(missing_skills) > 0, "Devrait avoir des compétences manquantes"
        assert 'python' in matching_skills, "Python devrait être dans les compétences correspondantes"
        assert 'django' in missing_skills, "Django devrait être dans les compétences manquantes"
        
        print("✅ Logique de base validée")
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans le test de logique: {e}")
        return False

def test_score_calculation_logic():
    """Test de la logique de calcul de scores (simulé)"""
    print("\n📊 Test de la logique de calcul de scores...")
    
    try:
        # Simuler des scores de matching
        mock_scores = [85.2, 72.1, 68.9, 45.3, 91.7, 55.8, 78.4]
        
        # Calculer les statistiques
        avg_score = sum(mock_scores) / len(mock_scores)
        max_score = max(mock_scores)
        min_score = min(mock_scores)
        
        # Distribution des scores
        high_scores = [s for s in mock_scores if s >= 75]
        medium_scores = [s for s in mock_scores if 50 <= s < 75]
        low_scores = [s for s in mock_scores if s < 50]
        
        print(f"✅ Score moyen: {avg_score:.1f}%")
        print(f"✅ Score max: {max_score:.1f}%")
        print(f"✅ Score min: {min_score:.1f}%")
        print(f"✅ Distribution: {len(high_scores)} élevés, {len(medium_scores)} moyens, {len(low_scores)} faibles")
        
        # Test de la logique de recommandation selon le score
        if avg_score < 40:
            priority = "🔴 PRIORITÉ HAUTE"
        elif avg_score < 65:
            priority = "🟡 PRIORITÉ MOYENNE"
        else:
            priority = "🟢 PROFIL SOLIDE"
        
        print(f"✅ Recommandation: {priority}")
        
        # Vérifications
        assert 0 <= avg_score <= 100, "Score moyen doit être entre 0 et 100"
        assert len(high_scores) + len(medium_scores) + len(low_scores) == len(mock_scores), "Distribution incorrecte"
        
        print("✅ Logique de calcul de scores validée")
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans le test de scores: {e}")
        return False

def main():
    """Fonction principale"""
    print("🧪 Test de la logique RH (sans serveur)")
    print("=" * 50)
    
    # Exécuter les tests
    tests_passed = 0
    total_tests = 3
    
    if test_detection_logic():
        tests_passed += 1
    
    if test_mock_recommendation():
        tests_passed += 1
    
    if test_score_calculation_logic():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Résultats finaux: {tests_passed}/{total_tests} tests réussis")
    
    if tests_passed == total_tests:
        print("🎉 Tous les tests de logique sont passés !")
        print("\n💡 La logique de base fonctionne correctement.")
        print("Pour tester avec de vraies données, démarrez le serveur Flask.")
    else:
        print("❌ Certains tests ont échoué")
        print("Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    main()
