#!/usr/bin/env python3
"""
Test avec des IDs réels de la base Supabase
"""

import requests
import json

def test_with_real_ids():
    url = "http://127.0.0.1:5000/chat/"
    
    data = {
        "query": "Développeur",
        "pageContext": "homepage"
    }
    
    # Test 1: Candidat réel
    real_candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"
    headers_candidate = {
        "Content-Type": "application/json",
        "X-User": json.dumps({"id": real_candidate_id, "role": "candidate"})
    }
    
    print("=== Test 1: Candidat réel ===")
    try:
        response = requests.post(url, headers=headers_candidate, json=data)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print(f"✅ Succès! Réponse: {response.json()['response'][:100]}...")
        else:
            print(f"❌ Erreur: {response.json()}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test 2: Recruteur réel
    real_recruiter_id = "6d2df96a-b6b2-4098-b5f3-1192fba544f7"
    headers_recruiter = {
        "Content-Type": "application/json",
        "X-User": json.dumps({"id": real_recruiter_id, "role": "recruiter"})
    }
    
    print("=== Test 2: Recruteur réel ===")
    try:
        response = requests.post(url, headers=headers_recruiter, json=data)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print(f"✅ Succès! Réponse: {response.json()['response'][:100]}...")
        else:
            print(f"❌ Erreur: {response.json()}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test 3: Candidat inexistant (doit échouer)
    fake_candidate_id = "00000000-0000-0000-0000-000000000000"
    headers_fake = {
        "Content-Type": "application/json",
        "X-User": json.dumps({"id": fake_candidate_id, "role": "candidate"})
    }
    
    print("=== Test 3: Candidat inexistant (doit échouer) ===")
    try:
        response = requests.post(url, headers=headers_fake, json=data)
        print(f"Status: {response.status_code}")
        if response.status_code == 404:
            print(f"✅ Erreur attendue: {response.json()}")
        else:
            print(f"❌ Réponse inattendue: {response.json()}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test 4: Sans header X-User (doit échouer)
    headers_no_user = {
        "Content-Type": "application/json"
    }
    
    print("=== Test 4: Sans header X-User (doit échouer) ===")
    try:
        response = requests.post(url, headers=headers_no_user, json=data)
        print(f"Status: {response.status_code}")
        if response.status_code == 401:
            print(f"✅ Erreur attendue: {response.json()}")
        else:
            print(f"❌ Réponse inattendue: {response.json()}")
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_with_real_ids()
