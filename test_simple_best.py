#!/usr/bin/env python3
"""
Test simple de "best offers"
"""

import requests
import json

def test_simple():
    url = "http://127.0.0.1:5000/chat/"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "query": "trouver best offers",
        "pageContext": "recherche_emploi",
        "user": {
            "id": "ed15f200-9b19-4bde-819d-f3b9a29b35e3",
            "role": "candidate"
        }
    }
    
    print("🧪 Test: 'trouver best offers'")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            response_text = result['response']
            
            if "**1." in response_text and "Compatibilité:" in response_text:
                print("✅ SUCCÈS! Recommandations avec scores détectées")
            elif "Je n'ai pas trouvé" in response_text:
                print("⚠️ Fallback - amélioration nécessaire")
            else:
                print("📝 Autre type de réponse")
            
            print(f"Réponse: {response_text[:200]}...")
            
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")

if __name__ == "__main__":
    test_simple()
