#!/usr/bin/env python3
"""
Generateur de questions variees pour tester votre chatbot
Tests avec differents types d'intentions et scenarios
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:5000/chat/"
USER_CANDIDATE = {
    "id": "63800628-55a2-4c33-97ce-5bcc5ee6a1bb",
    "role": "candidate"
}

USER_RECRUITER = {
    "id": "4db01413-00e5-4c1c-90f7-4c03438d1dc3", 
    "role": "recruiter"
}

# Questions variees par categorie
QUESTIONS_TEST = {
    "salutations": [
        "Bonjour !",
        "Salut, comment ca va ?",
        "Hello",
        "Hi there",
        "Bonsoir",
        "Coucou !"
    ],
    
    "recherche_emploi_generale": [
        "Je cherche un travail",
        "Quels sont les postes disponibles ?",
        "Montrez-moi les offres d'emploi",
        "Y a-t-il des opportunites ?",
        "Je veux voir les jobs",
        "Donnez-moi la liste des emplois"
    ],
    
    "recherche_specifique_competences": [
        "Je cherche un poste en Python",
        "Avez-vous des offres pour developpeur React ?",
        "Jobs en intelligence artificielle",
        "Postes data scientist",
        "Emploi developpeur full stack",
        "Offres en machine learning",
        "Travail avec TensorFlow",
        "Poste developpeur backend Node.js"
    ],
    
    "recherche_par_localisation": [
        "Jobs a Casablanca",
        "Emplois a Rabat", 
        "Postes en remote",
        "Travail a distance",
        "Offres a Paris",
        "Jobs hybrides",
        "Emploi sur site a Marrakech"
    ],
    
    "recherche_par_salaire": [
        "Postes avec salaire superieur a 50000",
        "Jobs bien payes",
        "Emplois avec bon salaire",
        "Offres plus de 15000 MAD",
        "Postes 70000 euros minimum"
    ],
    
    "recherche_par_contrat": [
        "Contrats CDI disponibles",
        "Jobs en CDD",
        "Missions freelance",
        "Stages disponibles",
        "Contrats temps plein"
    ],
    
    "recommandations_personnalisees": [
        "Recommandez-moi des offres",
        "Quels postes me correspondent ?",
        "Suggerez-moi des emplois",
        "Offres adaptees a mon profil",
        "Jobs qui matchent avec moi",
        "Proposez-moi des opportunites"
    ],
    
    "conseils_rh": [
        "Comment ameliorer mon CV ?",
        "Conseils pour mon profil",
        "Comment augmenter mes chances ?",
        "Que faire pour etre plus attractif ?",
        "Ameliorer mes competences",
        "Developper mon profil professionnel",
        "Comment me demarquer ?"
    ],
    
    "conseils_entretien": [
        "Comment preparer un entretien ?",
        "Conseils entretien technique",
        "Questions frequentes en entretien",
        "Comment reussir mon entretien ?",
        "Preparation entretien video",
        "Que dire en entretien ?",
        "Comment negocier le salaire ?"
    ],
    
    "questions_secteur": [
        "Tendances du marche IT",
        "Secteurs qui recrutent",
        "Competences demandees en 2024",
        "Evolution du marche emploi",
        "Technologies populaires",
        "Metiers d'avenir"
    ],

    "questions_pratiques": [
        "Comment postuler ?",
        "Processus de candidature",
        "Delais de reponse",
        "Etapes de recrutement",
        "Documents necessaires",
        "Suivi de candidature"
    ],
    
    "questions_complexes": [
        "Je suis developpeur Python avec 5 ans d'experience, je cherche un poste remote en IA avec salaire 80k+",
        "Reconversion vers la data science, quelles competences acquerir ?",
        "Freelance vs CDI dans le developpement web, que choisir ?",
        "Comment passer de junior a senior developer ?",
        "Negociation salaire pour poste senior backend",
        "Changer de ville pour un meilleur poste, conseils ?"
    ],
    
    "questions_recruteur": [
        "Comment trouver de bons candidats ?",
        "Profils Python disponibles",
        "Candidats pour poste senior",
        "Comment evaluer un developpeur ?",
        "Sourcing de talents IT",
        "Candidats avec experience React"
    ]
}

def test_question(query, user_data, context="", description=""):
    """Test une question specifique"""
    
    headers = {"Content-Type": "application/json"}
    
    body = {
        "query": query,
        "pageContext": context,
        "role": user_data["role"],
        "user": user_data
    }
    
    try:
        response = requests.post(BASE_URL, headers=headers, json=body, timeout=20)
        
        if response.status_code == 200:
            json_response = response.json()
            response_text = json_response.get('response', '')
            
            # Analyse rapide de la reponse
            analysis = {
                "length": len(response_text),
                "has_name": "Taoufiq" in response_text or "Mohammed" in response_text,
                "has_greeting": any(word in response_text.lower() for word in ["bonjour", "salut", "hello"]),
                "has_jobs": any(word in response_text.lower() for word in ["offre", "poste", "emploi", "job"]),
                "has_emojis": any(emoji in response_text for emoji in ["🏢", "📍", "💰", "🔧", "📝", "✅", "🎯", "💡"]),
                "structured": "**" in response_text or "*" in response_text,
                "actionable": any(phrase in response_text.lower() for phrase in ["n'hesitez", "contactez", "postulez"])
            }
            
            return {
                "success": True,
                "response": response_text,
                "analysis": analysis,
                "status": response.status_code
            }
        else:
            return {
                "success": False,
                "error": f"Status {response.status_code}",
                "response": response.text[:200]
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "response": ""
        }

def run_category_tests(category_name, questions, user_data, max_tests=3):
    """Test une categorie de questions"""
    
    print(f"\n{'='*60}")
    print(f"📋 CATEGORIE: {category_name.upper()}")
    print(f"👤 Utilisateur: {user_data['role']}")
    print(f"{'='*60}")
    
    results = []
    
    for i, question in enumerate(questions[:max_tests], 1):
        print(f"\n🔍 Test {i}: {question}")
        print("-" * 40)
        
        result = test_question(question, user_data)
        results.append(result)
        
        if result["success"]:
            print("✅ SUCCESS")
            
            analysis = result["analysis"]
            score = sum(analysis.values()) / len(analysis) * 100
            
            print(f"📊 Score qualite: {score:.0f}%")
            print(f"📏 Longueur: {analysis['length']} caracteres")
            
            # Indicateurs cles
            indicators = []
            if analysis["has_name"]: indicators.append("👤 Nom")
            if analysis["has_greeting"]: indicators.append("👋 Salutation") 
            if analysis["has_jobs"]: indicators.append("💼 Offres")
            if analysis["has_emojis"]: indicators.append("😊 Emojis")
            if analysis["structured"]: indicators.append("📋 Structure")
            if analysis["actionable"]: indicators.append("🎯 Action")
            
            print(f"🔍 Indicateurs: {' | '.join(indicators) if indicators else 'Aucun'}")
            
            # Apercu de la reponse
            preview = result["response"][:100].replace('\n', ' ')
            print(f"📝 Apercu: {preview}...")
            
        else:
            print(f"❌ FAILED: {result['error']}")
        
        # Pause entre les tests
        time.sleep(1)
    
    return results

def generate_summary(all_results):
    """Genere un resume des tests"""
    
    total_tests = sum(len(results) for results in all_results.values())
    successful_tests = sum(sum(1 for r in results if r["success"]) for results in all_results.values())
    
    print(f"\n{'='*60}")
    print("📊 RESUME GENERAL")
    print(f"{'='*60}")
    print(f"📈 Tests totaux: {total_tests}")
    print(f"✅ Tests reussis: {successful_tests}")
    print(f"❌ Tests echoues: {total_tests - successful_tests}")
    print(f"🎯 Taux de reussite: {(successful_tests/total_tests*100):.1f}%")
    
    # Analyse par categorie
    print(f"\n📋 Resultats par categorie:")
    for category, results in all_results.items():
        success_count = sum(1 for r in results if r["success"])
        total_count = len(results)
        rate = (success_count / total_count * 100) if total_count > 0 else 0
        print(f"  📂 {category}: {success_count}/{total_count} ({rate:.0f}%)")

def main():
    """Fonction principale"""
    
    print("🚀 Test de Questions Variees pour votre Chatbot")
    print("Tests avec differents types d'intentions et scenarios")
    print(f"🌐 URL: {BASE_URL}")
    
    # Verifier la connexion
    try:
        test_response = requests.get("http://localhost:5000/", timeout=3)
        print("✅ Serveur accessible")
    except:
        print("❌ Serveur non accessible")
        print("💡 Demarrez le serveur avec: python run.py")
        return
    
    all_results = {}
    
    # Tests pour candidat
    print(f"\n🎯 TESTS POUR CANDIDAT (Taoufiq)")
    categories_candidat = [
        "salutations", "recherche_emploi_generale", "recherche_specifique_competences",
        "recherche_par_localisation", "recommandations_personnalisees", "conseils_rh",
        "conseils_entretien", "questions_complexes"
    ]
    
    for category in categories_candidat:
        if category in QUESTIONS_TEST:
            results = run_category_tests(category, QUESTIONS_TEST[category], USER_CANDIDATE, max_tests=2)
            all_results[f"candidat_{category}"] = results
    
    # Tests pour recruteur
    print(f"\n🎯 TESTS POUR RECRUTEUR")
    results = run_category_tests("questions_recruteur", QUESTIONS_TEST["questions_recruteur"], USER_RECRUITER, max_tests=2)
    all_results["recruteur_questions"] = results
    
    # Resume final
    generate_summary(all_results)
    
    print(f"\n🎉 Tests termines!")
    print(f"📋 Votre chatbot a ete teste avec {sum(len(r) for r in all_results.values())} questions variees")
    print(f"🤖 L'analyse d'intention LLM et la generation de reponses fonctionnent!")

if __name__ == "__main__":
    main()
