# -*- coding: utf-8 -*-
"""
Service LLM avec OpenRouter pour l'analyse d'intention et generation de reponses
"""

import requests
import json
import os
import re
from typing import Dict, List, Any, Optional
from flask import current_app

class OpenRouterLLMService:
    def __init__(self):
        self.api_key = os.getenv("OPENROUTER_API_KEY")
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.model = os.getenv("OPENROUTER_MODEL", "anthropic/claude-3.5-sonnet")
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": os.getenv("OPENROUTER_REFERER", "http://localhost:5000"),
            "X-Title": os.getenv("OPENROUTER_TITLE", "Job Matching Chatbot")
        }
    
    def _make_llm_request(self, messages: List[Dict], max_tokens: int = 1000, temperature: float = 0.3) -> Optional[str]:
        """
        Fait un appel a l'API OpenRouter
        """
        try:
            if not self.api_key:
                print("[WARNING] OPENROUTER_API_KEY non configuree, utilisation du fallback")
                return None
            
            payload = {
                "model": self.model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": 0.9,
                "frequency_penalty": 0.1,
                "presence_penalty": 0.1
            }
            
            print(f"[LLM] Appel OpenRouter avec modele: {self.model}")
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'choices' in data and len(data['choices']) > 0:
                    content = data['choices'][0]['message']['content']
                    print(f"[LLM] Reponse recue: {len(content)} caracteres")
                    return self._clean_unicode_response(content)
                else:
                    print(f"[LLM ERROR] Reponse invalide: {data}")
                    return None
            else:
                print(f"[LLM ERROR] Status {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            print(f"[LLM ERROR] Exception: {e}")
            return None
    
    def _clean_unicode_response(self, text: str) -> str:
        """
        Nettoie les caracteres Unicode problematiques
        """
        try:
            replacements = {
                '\u2019': "'",
                '\u2018': "'",
                '\u201c': '"',
                '\u201d': '"',
                '\u2013': '-',
                '\u2014': '--',
                '\u2026': '...'
            }
            
            for unicode_char, replacement in replacements.items():
                text = text.replace(unicode_char, replacement)
            
            text = text.encode('utf-8', errors='ignore').decode('utf-8')
            return text
            
        except Exception as e:
            print(f"[WARNING] Erreur nettoyage Unicode: {e}")
            return text
    
    def analyze_user_intent(self, query: str, page_context: str, user_role: str) -> Dict[str, Any]:
        """
        Analyse l'intention de l'utilisateur avec LLM
        """
        try:
            system_prompt = """Tu es un assistant IA specialise dans l'analyse d'intentions pour un chatbot de recherche d'emploi.

Ton role est d'analyser la requete de l'utilisateur et de determiner quel service appeler.

Services disponibles:
1. "recommendation_rh" - Conseils pour ameliorer le profil candidat
2. "recommendation_jobs" - Recommandations d'offres personnalisees pour candidats
3. "recommendation_candidates" - Recommandations de candidats pour recruteurs
4. "general_advice" - Conseils generaux
5. "rag_search" - Recherche d'offres d'emploi (par defaut)

Reponds UNIQUEMENT avec un JSON valide contenant:
{
  "service": "nom_du_service",
  "confidence": 0.0-1.0,
  "reasoning": "explication courte",
  "extracted_data": {"keywords": ["mots-cles"]}
}"""

            user_prompt = f"""Analyse cette requete:

Requete: "{query}"
Contexte de page: "{page_context}"
Role utilisateur: "{user_role}"

Determine le service approprie et reponds avec le JSON."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = self._make_llm_request(messages, max_tokens=300, temperature=0.1)
            
            if response:
                try:
                    json_match = re.search(r'\{.*\}', response, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(0)
                        result = json.loads(json_str)
                        
                        if all(key in result for key in ["service", "confidence", "reasoning"]):
                            print(f"[LLM] Intention analysee: {result['service']} (confiance: {result['confidence']})")
                            return result
                        else:
                            print(f"[LLM WARNING] JSON incomplet: {result}")
                    else:
                        print(f"[LLM WARNING] Pas de JSON trouve dans: {response}")
                        
                except json.JSONDecodeError as e:
                    print(f"[LLM ERROR] JSON invalide: {e}")
            
            return self._fallback_intent_analysis(query, page_context, user_role)
            
        except Exception as e:
            print(f"[LLM ERROR] Erreur analyse intention: {e}")
            return self._fallback_intent_analysis(query, page_context, user_role)
    
    def _fallback_intent_analysis(self, query: str, page_context: str, user_role: str) -> Dict[str, Any]:
        """
        Analyse d'intention de fallback sans LLM
        """
        query_lower = query.lower()
        
        rh_keywords = ['ameliorer', 'conseil', 'competences', 'score', 'developper', 'formation', 'aide']
        if any(keyword in query_lower for keyword in rh_keywords) and user_role == "candidate":
            return {
                "service": "recommendation_rh",
                "confidence": 0.8,
                "reasoning": "Mots-cles RH detectes",
                "extracted_data": {"keywords": [k for k in rh_keywords if k in query_lower]}
            }
        
        job_rec_keywords = ['recommande', 'suggere', 'offres pour moi', 'opportunites']
        if any(keyword in query_lower for keyword in job_rec_keywords) and user_role == "candidate":
            return {
                "service": "recommendation_jobs",
                "confidence": 0.8,
                "reasoning": "Demande de recommandations d'offres",
                "extracted_data": {"keywords": [k for k in job_rec_keywords if k in query_lower]}
            }
        
        advice_keywords = ['aide', 'comment', 'entretien', 'cv', 'salaire', 'contrat']
        if any(keyword in query_lower for keyword in advice_keywords):
            return {
                "service": "general_advice",
                "confidence": 0.7,
                "reasoning": "Demande de conseils generaux",
                "extracted_data": {"keywords": [k for k in advice_keywords if k in query_lower]}
            }
        
        return {
            "service": "rag_search",
            "confidence": 0.6,
            "reasoning": "Recherche d'offres par defaut",
            "extracted_data": {"keywords": query_lower.split()[:3]}
        }
    
    def generate_final_response(self, query: str, jobs: List[Dict], user_role: str, display_name: str, intent_analysis: Dict) -> str:
        """
        Genere la reponse finale avec LLM en utilisant les donnees RAG
        """
        try:
            if not jobs:
                return self._generate_no_jobs_response(query, user_role, display_name)
            
            jobs_context = self._prepare_jobs_context(jobs)
            
            system_prompt = f"""Tu es un assistant IA specialise dans le recrutement et la recherche d'emploi.

Ton role est de presenter les offres d'emploi trouvees de maniere personnalisee et engageante.

Instructions:
1. Salue l'utilisateur par son nom: {display_name}
2. Presente les offres de maniere structuree et attractive
3. Utilise des emojis pour rendre la reponse plus visuelle
4. Donne des conseils pertinents selon le role: {user_role}
5. Reponds en francais
6. Sois concis mais informatif
7. Encourage l'utilisateur a agir

Format souhaite:
- Salutation personnalisee
- Presentation des offres avec details cles
- Conseils ou encouragements
- Appel a l'action"""

            user_prompt = f"""L'utilisateur "{display_name}" (role: {user_role}) a recherche: "{query}"

Voici les offres trouvees:
{jobs_context}

Intention detectee: {intent_analysis.get('reasoning', 'Recherche d emploi')}

Genere une reponse personnalisee et engageante."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = self._make_llm_request(messages, max_tokens=1500, temperature=0.4)
            
            if response:
                return response
            else:
                return self._generate_simple_response(query, jobs, display_name)
                
        except Exception as e:
            print(f"[LLM ERROR] Erreur generation reponse: {e}")
            return self._generate_simple_response(query, jobs, display_name)
    
    def _prepare_jobs_context(self, jobs: List[Dict]) -> str:
        """
        Prepare le contexte des offres pour le LLM
        """
        context = ""
        for i, job in enumerate(jobs[:5], 1):
            context += f"\n{i}. {job.get('title', 'Poste')}\n"
            context += f"   - Entreprise: {job.get('company_name', 'Non specifiee')}\n"
            context += f"   - Lieu: {job.get('location', 'Non specifie')}\n"
            context += f"   - Contrat: {job.get('contract_type', 'Non specifie')}\n"
            
            if job.get('skills'):
                skills = job['skills'][:3] if isinstance(job['skills'], list) else []
                context += f"   - Competences: {', '.join(skills)}\n"
            
            if job.get('salary_min') and job.get('salary_max'):
                context += f"   - Salaire: {job['salary_min']}-{job['salary_max']} {job.get('salary_currency', 'EUR')}\n"
            
            description = job.get('description', '')
            if description:
                short_desc = description[:100] + "..." if len(description) > 100 else description
                context += f"   - Description: {short_desc}\n"
        
        return context
    
    def _generate_no_jobs_response(self, query: str, user_role: str, display_name: str) -> str:
        """
        Genere une reponse quand aucune offre n'est trouvee
        """
        return f"""Bonjour {display_name} !

Je n'ai pas trouve d'offres correspondant exactement a "{query}" dans notre base de donnees actuelle.

**Suggestions :**
- Essayez avec des mots-cles differents ou plus generaux
- Verifiez l'orthographe de votre recherche
- Consultez regulierement car de nouvelles offres sont ajoutees

**Je peux vous aider avec :**
- Conseils pour ameliorer votre CV
- Preparation aux entretiens
- Informations sur les types de contrats
- Conseils sur les competences a developper

N'hesitez pas a me poser une question plus specifique !"""
    
    def _generate_simple_response(self, query: str, jobs: List[Dict], display_name: str) -> str:
        """
        Genere une reponse simple sans LLM
        """
        response = f"Bonjour {display_name} ! Voici les offres d'emploi que j'ai trouvees pour \"{query}\" :\n\n"
        
        for i, job in enumerate(jobs[:3], 1):
            response += f"**{i}. {job.get('title', 'Poste')}**\n"
            response += f"🏢 Entreprise: {job.get('company_name', 'Non specifiee')}\n"
            response += f"📍 Lieu: {job.get('location', 'Non specifie')}\n"
            
            if job.get('contract_type'):
                response += f"📄 Contrat: {job.get('contract_type')}\n"
            
            skills = job.get('skills', [])
            if skills and isinstance(skills, list):
                skills_str = ', '.join(skills[:3])
                response += f"🔧 Competences: {skills_str}\n"
            
            response += "\n"
        
        response += "💡 Ces offres correspondent a votre recherche. N'hesitez pas a me poser des questions plus specifiques !"
        
        return response

# Instance globale du service
_llm_service = None

def get_openrouter_llm_service() -> OpenRouterLLMService:
    """
    Retourne l'instance du service LLM OpenRouter
    """
    global _llm_service
    if _llm_service is None:
        _llm_service = OpenRouterLLMService()
    return _llm_service
