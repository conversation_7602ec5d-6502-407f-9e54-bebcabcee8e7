#!/usr/bin/env python3
"""
Exemple d'utilisation de la fonction recommendation_candidate_rh
"""

import requests
import json
import base64

def example_usage():
    """Exemple simple d'utilisation de la fonction RH"""
    
    # Configuration
    BASE_URL = "http://localhost:5000"
    
    # Données du candidat (remplacez par des vraies données)
    candidate_data = {
        "id": "your-real-candidate-uuid-here",  # ⚠️ Remplacez par un UUID réel
        "role": "candidate"
    }
    
    print("🤖 Exemple d'utilisation - Recommandations RH")
    print("=" * 50)

    # Encoder les données utilisateur
    user_json = json.dumps(candidate_data)
    user_b64 = base64.b64encode(user_json.encode('utf-8')).decode('utf-8')

    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }

    # Via chat avec query RH
    print("\n💬 Test des recommandations RH via chat")
    print("-" * 40)
    
    queries_examples = [
        "Comment améliorer mon profil ?",
        "Quelles compétences dois-je développer ?",
        "Conseil pour augmenter mon score de matching"
    ]
    
    for query in queries_examples:
        print(f"\nQuery: '{query}'")
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/chat/",
                headers=headers,
                json={
                    "query": query,
                    "pageContext": "conseil"
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Détection automatique réussie!")
                print(f"Réponse: {data['response'][:200]}...")
            else:
                print(f"❌ Erreur {response.status_code}: {response.json()}")
                
        except Exception as e:
            print(f"❌ Erreur de connexion: {e}")

def test_with_real_data():
    """Test avec de vraies données - à adapter selon votre base"""
    
    print("\n🔧 Configuration pour test avec vraies données")
    print("-" * 50)
    
    # Instructions pour l'utilisateur
    print("Pour tester avec de vraies données:")
    print("1. Trouvez un UUID de candidat dans votre base de données")
    print("2. Remplacez 'your-real-candidate-uuid-here' dans le code")
    print("3. Assurez-vous que le serveur Flask est démarré")
    print("4. Exécutez ce script")
    
    print("\nExemple de requête SQL pour trouver un candidat:")
    print("SELECT id, full_name FROM candidates LIMIT 1;")
    
    print("\nExemple de données candidate_profiles:")
    print("SELECT candidate_id, skillner_skills, py_skills FROM candidate_profiles LIMIT 1;")

if __name__ == "__main__":
    print("🚀 Démarrage de l'exemple d'utilisation")
    
    # Vérifier la configuration
    candidate_id = "your-real-candidate-uuid-here"
    if candidate_id == "your-real-candidate-uuid-here":
        print("\n⚠️  ATTENTION: Vous devez configurer un vrai UUID de candidat!")
        test_with_real_data()
        
        # Demander si l'utilisateur veut continuer avec des données de test
        continue_test = input("\nVoulez-vous continuer avec des données de test ? (y/N): ")
        if continue_test.lower() != 'y':
            print("Test annulé.")
            exit()
    
    # Exécuter l'exemple
    example_usage()
    
    print("\n" + "=" * 50)
    print("✨ Exemple terminé!")
    print("\nPour une utilisation en production:")
    print("- Remplacez l'UUID par de vraies données")
    print("- Gérez les erreurs appropriément")
    print("- Ajoutez de la validation côté client")
    print("- Implémentez un cache si nécessaire")
