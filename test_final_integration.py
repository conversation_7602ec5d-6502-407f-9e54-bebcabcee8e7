#!/usr/bin/env python3
"""
Test final d'intégration de la fonction recommendation_candidate_rh
"""

import sys
import os
import json
import base64

# Ajouter le répertoire racine au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_recommendation_function_directly():
    """Test direct de la fonction recommendation_candidate_rh"""
    print("🎯 Test direct de la fonction recommendation_candidate_rh...")
    
    try:
        from app import create_app
        from app.routes.chat import recommendation_candidate_rh
        
        app = create_app()
        
        with app.app_context():
            supabase = app.supabase
            
            # Récupérer un candidat avec profil
            candidate = supabase.table("candidates").select("id, full_name").limit(1).execute()
            
            if not candidate.data:
                print("⚠️  Aucun candidat trouvé")
                return False
            
            candidate_id = candidate.data[0]['id']
            candidate_name = candidate.data[0].get('full_name', 'Test User')
            
            print(f"✅ Test avec candidat: {candidate_name}")
            
            # Appeler directement la fonction
            print("🔄 Appel de recommendation_candidate_rh...")
            result = recommendation_candidate_rh(candidate_id, candidate_name)
            
            # Vérifier le résultat
            if result and isinstance(result, str):
                print("✅ Fonction exécutée avec succès")
                print(f"✅ Longueur de la réponse: {len(result)} caractères")
                
                # Vérifier la présence d'éléments clés
                key_elements = [
                    "ANALYSE DE VOTRE PROFIL",
                    "SCORE ACTUEL MOYEN",
                    "POINTS FORTS",
                    "COMPÉTENCES À DÉVELOPPER",
                    "RECOMMANDATIONS PERSONNALISÉES"
                ]
                
                found_elements = [elem for elem in key_elements if elem in result]
                print(f"✅ Éléments clés trouvés: {len(found_elements)}/{len(key_elements)}")
                
                # Afficher un extrait
                print("\n📄 Extrait de la réponse:")
                print("-" * 50)
                print(result[:500] + "..." if len(result) > 500 else result)
                print("-" * 50)
                
                return len(found_elements) >= 3
            else:
                print("❌ Fonction n'a pas retourné de résultat valide")
                return False
                
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chat_endpoint_simulation():
    """Test de simulation de l'endpoint chat"""
    print("\n💬 Test de simulation de l'endpoint chat...")
    
    try:
        from app import create_app
        from app.routes.chat import detect_rh_recommendation_intent, recommendation_candidate_rh
        
        app = create_app()
        
        with app.app_context():
            supabase = app.supabase
            
            # Récupérer un candidat
            candidate = supabase.table("candidates").select("id, full_name").limit(1).execute()
            
            if not candidate.data:
                print("⚠️  Aucun candidat trouvé")
                return False
            
            candidate_id = candidate.data[0]['id']
            candidate_name = candidate.data[0].get('full_name', 'Test User')
            
            # Simuler différentes queries
            test_queries = [
                "Comment améliorer mon profil ?",
                "Quelles compétences dois-je développer ?",
                "Conseil pour augmenter mon score de matching",
                "Aide moi à progresser dans ma carrière"
            ]
            
            successful_tests = 0
            
            for query in test_queries:
                print(f"\n🔍 Test query: '{query}'")
                
                # 1. Test de détection
                is_rh_intent = detect_rh_recommendation_intent(query)
                print(f"   Détection RH: {is_rh_intent}")
                
                if is_rh_intent:
                    # 2. Test de la fonction de recommandation
                    try:
                        result = recommendation_candidate_rh(candidate_id, candidate_name)
                        if result and "ANALYSE DE VOTRE PROFIL" in result:
                            print("   ✅ Recommandation générée avec succès")
                            successful_tests += 1
                        else:
                            print("   ❌ Recommandation invalide")
                    except Exception as e:
                        print(f"   ❌ Erreur dans la recommandation: {e}")
                else:
                    print("   ❌ Query non détectée comme RH")
            
            print(f"\n📊 Tests réussis: {successful_tests}/{len(test_queries)}")
            return successful_tests >= len(test_queries) // 2
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_edge_cases():
    """Test des cas limites"""
    print("\n🔍 Test des cas limites...")
    
    try:
        from app import create_app
        from app.routes.chat import recommendation_candidate_rh
        
        app = create_app()
        
        with app.app_context():
            # Test avec un candidat inexistant
            print("🔸 Test avec candidat inexistant...")
            fake_id = "00000000-0000-0000-0000-000000000000"
            result = recommendation_candidate_rh(fake_id, "Test User")
            
            if "Je n'ai pas pu accéder à votre profil" in result:
                print("✅ Gestion correcte du candidat inexistant")
                edge_case_1 = True
            else:
                print("❌ Gestion incorrecte du candidat inexistant")
                edge_case_1 = False
            
            # Test avec nom vide
            print("\n🔸 Test avec nom vide...")
            supabase = app.supabase
            candidate = supabase.table("candidates").select("id").limit(1).execute()
            
            if candidate.data:
                candidate_id = candidate.data[0]['id']
                result = recommendation_candidate_rh(candidate_id, "")
                
                if result and len(result) > 0:
                    print("✅ Gestion correcte du nom vide")
                    edge_case_2 = True
                else:
                    print("❌ Problème avec nom vide")
                    edge_case_2 = False
            else:
                print("⚠️  Pas de candidat pour tester le nom vide")
                edge_case_2 = True
            
            return edge_case_1 and edge_case_2
            
    except Exception as e:
        print(f"❌ Erreur dans les cas limites: {e}")
        return False

def test_performance():
    """Test de performance"""
    print("\n⏱️  Test de performance...")
    
    try:
        import time
        from app import create_app
        from app.routes.chat import recommendation_candidate_rh
        
        app = create_app()
        
        with app.app_context():
            supabase = app.supabase
            candidate = supabase.table("candidates").select("id, full_name").limit(1).execute()
            
            if not candidate.data:
                print("⚠️  Aucun candidat pour le test de performance")
                return True
            
            candidate_id = candidate.data[0]['id']
            candidate_name = candidate.data[0].get('full_name', 'Test User')
            
            # Mesurer le temps d'exécution
            start_time = time.time()
            result = recommendation_candidate_rh(candidate_id, candidate_name)
            end_time = time.time()
            
            duration = end_time - start_time
            
            print(f"⏱️  Temps d'exécution: {duration:.2f} secondes")
            
            if duration < 30:
                print("✅ Performance acceptable (<30s)")
                return True
            else:
                print("⚠️  Performance lente (>30s)")
                return False
                
    except Exception as e:
        print(f"❌ Erreur dans le test de performance: {e}")
        return False

def main():
    """Fonction principale"""
    print("🧪 Test Final d'Intégration - recommendation_candidate_rh")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 4
    
    # Test direct de la fonction
    if test_recommendation_function_directly():
        tests_passed += 1
    
    # Test de simulation de l'endpoint
    if test_chat_endpoint_simulation():
        tests_passed += 1
    
    # Test des cas limites
    if test_edge_cases():
        tests_passed += 1
    
    # Test de performance
    if test_performance():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Résultats finaux: {tests_passed}/{total_tests} tests réussis")
    
    if tests_passed == total_tests:
        print("🎉 TOUS LES TESTS SONT PASSÉS !")
        print("\n✅ La fonction recommendation_candidate_rh est prête pour la production")
        print("\n🚀 Prochaines étapes:")
        print("1. Démarrer le serveur Flask: python run.py")
        print("2. Tester via l'API avec des requêtes HTTP")
        print("3. Intégrer dans le frontend")
        
    elif tests_passed >= 2:
        print("✅ Tests principaux réussis")
        print("⚠️  Quelques améliorations possibles")
        print("\n💡 La fonction est fonctionnelle mais peut être optimisée")
        
    else:
        print("❌ Tests principaux échoués")
        print("🔧 Vérifiez les erreurs et corrigez avant la production")

if __name__ == "__main__":
    main()
