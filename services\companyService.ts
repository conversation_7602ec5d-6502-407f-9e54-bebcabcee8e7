// Récupérer une entreprise par recruiter_id
export const getCompanyByRecruiterId = async (recruiterId: string) => {
  try {
    const response = await fetch(`http://localhost:5000/company/recruiter/${recruiterId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching company:', error);
    throw error;
  }
};

// Mettre à jour une entreprise par recruiter_id
export const updateCompanyByRecruiterId = async (recruiterId: string, companyData: any) => {
  try {
    const response = await fetch(`http://localhost:5000/company/recruiter/${recruiterId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(companyData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error updating company:', error);
    throw error;
  }
};