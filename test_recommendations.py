#!/usr/bin/env python3
"""
Test des fonctionnalités de recommandation du chatbot
"""

import requests
import json
import base64

def test_recommendation(user_id, role, query, page_context, description):
    """Test une recommandation spécifique"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": user_id, "role": role}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"🎯 {description}")
    print(f"User: {user_id[:8]}... (role: {role})")
    print(f"Query: {query}")
    print(f"Context: {page_context}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=90)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès!")
            response_text = result['response']
            
            # Extraire le nom d'utilisateur de la réponse
            if "Bonjour" in response_text:
                greeting_line = response_text.split('\n')[0]
                print(f"👋 Salutation: {greeting_line}")
            
            # Compter les recommandations
            if "**1." in response_text:
                recommendation_count = response_text.count("**") // 2
                print(f"📊 Nombre de recommandations: {recommendation_count}")
            
            if len(response_text) > 400:
                print(f"📝 Réponse: {response_text[:400]}...")
                print(f"[Réponse complète: {len(response_text)} caractères]")
            else:
                print(f"📝 Réponse: {response_text}")
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")
    
    print("\n" + "="*80 + "\n")

def main():
    print("🎯 Test des fonctionnalités de recommandation")
    print("="*80)
    
    # IDs des utilisateurs
    recruiter_id = "e714bb4b-b3f8-4343-8d5d-1e120e421b29"
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"
    
    # Test 1: Candidat demande des recommandations d'offres
    test_recommendation(
        candidate_id, 
        "candidate",
        "Peux-tu me recommander des offres d'emploi qui correspondent à mon profil ?", 
        "recherche_emploi",
        "Test 1: Candidat demande recommandations d'offres"
    )
    
    # Test 2: Candidat avec mots-clés de recommandation
    test_recommendation(
        candidate_id, 
        "candidate",
        "Quelles sont les opportunités qui me conviennent ?", 
        "recherche_emploi",
        "Test 2: Candidat - opportunités qui conviennent"
    )
    
    # Test 3: Candidat - matching
    test_recommendation(
        candidate_id, 
        "candidate",
        "Trouve-moi des emplois en matching avec mes compétences", 
        "recherche_emploi",
        "Test 3: Candidat - matching compétences"
    )
    
    # Test 4: Recruteur demande candidats (sans job ID)
    test_recommendation(
        recruiter_id, 
        "recruiter",
        "Peux-tu me recommander des candidats pour mon offre ?", 
        "recherche_emploi",
        "Test 4: Recruteur demande candidats (sans job ID)"
    )
    
    # Test 5: Recruteur avec un job ID fictif
    test_recommendation(
        recruiter_id, 
        "recruiter",
        "Recommande-moi des candidats pour l'offre 12345678-1234-1234-1234-123456789012", 
        "recherche_emploi",
        "Test 5: Recruteur avec job ID fictif"
    )
    
    # Test 6: Candidat - recherche normale (pas de recommandation)
    test_recommendation(
        candidate_id, 
        "candidate",
        "Développeur Python", 
        "recherche_emploi",
        "Test 6: Candidat - recherche normale (contrôle)"
    )
    
    # Test 7: Recruteur - recherche normale (pas de recommandation)
    test_recommendation(
        recruiter_id, 
        "recruiter",
        "Développeur Python", 
        "recherche_emploi",
        "Test 7: Recruteur - recherche normale (contrôle)"
    )

if __name__ == "__main__":
    main()
