#!/usr/bin/env python3
"""
Générateur de commandes curl corrigées pour tester le chatbot
"""

import json
import base64

def generate_curl_commands():
    """Génère des commandes curl prêtes à utiliser"""
    
    # IDs valides de ta base de données
    candidates = [
        {"id": "4db01413-00e5-4c1c-90f7-4c03438d1dc3", "name": "Mohammed Amine MAHID"},
        {"id": "ed15f200-9b19-4bde-819d-f3b9a29b35e3", "name": "<PERSON>"},
        {"id": "a482edbc-246b-4521-bf08-95d568c7900e", "name": "<PERSON>"}
    ]
    
    recruiters = [
        {"id": "6d2df96a-b6b2-4098-b5f3-1192fba544f7", "name": "<PERSON><PERSON>ru<PERSON><PERSON>"},
        {"id": "479078c8-e00d-4a13-9d4e-68d93a18b5f8", "name": "<PERSON>"}
    ]
    
    # Questions de test
    questions = [
        {"query": "Développeur Python", "pageContext": "recherche_emploi"},
        {"query": "Comment améliorer mon CV ?", "pageContext": "conseil"},
        {"query": "Frontend React", "pageContext": "recherche_emploi"},
        {"query": "Entretien développeur", "pageContext": "entretien"}
    ]
    
    print("🚀 Commandes curl corrigées pour tester le chatbot")
    print("="*70)
    
    # Générer des commandes pour candidats
    print("\n📋 Tests avec CANDIDATS:")
    print("-" * 40)
    
    for i, candidate in enumerate(candidates[:2], 1):  # Prendre les 2 premiers
        user_data = {"id": candidate["id"], "role": "candidate"}
        user_json = json.dumps(user_data)
        user_b64 = base64.b64encode(user_json.encode('utf-8')).decode('utf-8')
        
        question = questions[i-1] if i-1 < len(questions) else questions[0]
        
        print(f"\n{i}. Test candidat: {candidate['name']}")
        print(f"   ID: {candidate['id']}")
        print(f"   Header base64: {user_b64}")
        print(f"   Commande curl:")
        print(f"""curl -X POST http://127.0.0.1:5000/chat/ \\
  -H "Content-Type: application/json" \\
  -H "X-User: {user_b64}" \\
  -d '{{"query": "{question['query']}", "pageContext": "{question['pageContext']}"}}'""")
    
    # Générer des commandes pour recruteurs
    print("\n🏢 Tests avec RECRUTEURS:")
    print("-" * 40)
    
    for i, recruiter in enumerate(recruiters[:1], 1):  # Prendre le premier
        user_data = {"id": recruiter["id"], "role": "recruiter"}
        user_json = json.dumps(user_data)
        user_b64 = base64.b64encode(user_json.encode('utf-8')).decode('utf-8')
        
        question = {"query": "Profil développeur senior", "pageContext": "recrutement"}
        
        print(f"\n{i}. Test recruteur: {recruiter['name']}")
        print(f"   ID: {recruiter['id']}")
        print(f"   Header base64: {user_b64}")
        print(f"   Commande curl:")
        print(f"""curl -X POST http://127.0.0.1:5000/chat/ \\
  -H "Content-Type: application/json" \\
  -H "X-User: {user_b64}" \\
  -d '{{"query": "{question['query']}", "pageContext": "{question['pageContext']}"}}'""")
    
    # Tests d'erreur
    print("\n❌ Tests d'ERREURS:")
    print("-" * 40)
    
    # Test utilisateur inexistant
    fake_user_data = {"id": "00000000-0000-0000-0000-000000000000", "role": "candidate"}
    fake_user_json = json.dumps(fake_user_data)
    fake_user_b64 = base64.b64encode(fake_user_json.encode('utf-8')).decode('utf-8')
    
    print(f"\n1. Test utilisateur inexistant (doit retourner 404):")
    print(f"""curl -X POST http://127.0.0.1:5000/chat/ \\
  -H "Content-Type: application/json" \\
  -H "X-User: {fake_user_b64}" \\
  -d '{{"query": "Test", "pageContext": "test"}}'""")
    
    print(f"\n2. Test sans header X-User (doit retourner 401):")
    print(f"""curl -X POST http://127.0.0.1:5000/chat/ \\
  -H "Content-Type: application/json" \\
  -d '{{"query": "Test", "pageContext": "test"}}'""")
    
    print(f"\n3. Test header invalide (doit retourner 400):")
    print(f"""curl -X POST http://127.0.0.1:5000/chat/ \\
  -H "Content-Type: application/json" \\
  -H "X-User: invalid_base64_header" \\
  -d '{{"query": "Test", "pageContext": "test"}}'""")
    
    print("\n" + "="*70)
    print("💡 CORRECTION pour ton script bash:")
    print("   Remplace: USER_B64=$(echo -n \"$USER_JSON\" | base64)")
    print("   Par:      USER_B64=$(echo -n \"$USER_JSON\" | base64 | tr -d '\\n')")
    print("\n🎯 Toutes ces commandes devraient maintenant fonctionner!")

if __name__ == "__main__":
    generate_curl_commands()
