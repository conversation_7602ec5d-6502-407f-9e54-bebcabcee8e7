#!/usr/bin/env python3
"""
Test du chatbot avec les vraies données de la base
"""

import requests
import json
import base64

def test_query(user_id, role, query, page_context, description):
    """Test une query spécifique"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": user_id, "role": role}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"🎯 {description}")
    print(f"User: {user_id[:8]}... (role: {role})")
    print(f"Query: {query}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès!")
            response_text = result['response']
            
            # Extraire le nom d'utilisateur
            if "Bonjour" in response_text:
                greeting_line = response_text.split('\n')[0]
                print(f"👋 Salutation: {greeting_line}")
            
            # Compter les recommandations/offres
            if "**1." in response_text:
                count = response_text.count("**") // 2
                print(f"📊 Nombre d'éléments: {count}")
            
            if len(response_text) > 350:
                print(f"📝 Réponse: {response_text[:350]}...")
                print(f"[Réponse complète: {len(response_text)} caractères]")
            else:
                print(f"📝 Réponse: {response_text}")
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")
    
    print("\n" + "="*70 + "\n")

def main():
    print("🎯 Test du chatbot avec les vraies données")
    print("="*70)
    
    # IDs des utilisateurs réels
    recruiter_id = "e714bb4b-b3f8-4343-8d5d-1e120e421b29"  # John Doe
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"  # Mohamed ouabbi
    
    # Test 1: Candidat - Recherche Data Scientist
    test_query(
        candidate_id, 
        "candidate",
        "Data Scientist", 
        "recherche_emploi",
        "Test 1: Candidat cherche Data Scientist"
    )
    
    # Test 2: Candidat - Recherche Développeur Python
    test_query(
        candidate_id, 
        "candidate",
        "Développeur Python", 
        "recherche_emploi",
        "Test 2: Candidat cherche Développeur Python"
    )
    
    # Test 3: Candidat - Recommandations personnalisées
    test_query(
        candidate_id, 
        "candidate",
        "Recommande-moi des offres qui correspondent à mon profil", 
        "recherche_emploi",
        "Test 3: Candidat demande recommandations"
    )
    
    # Test 4: Recruteur - Recherche candidats pour Data Scientist
    test_query(
        recruiter_id, 
        "recruiter",
        "Candidats pour Data Scientist", 
        "recherche_emploi",
        "Test 4: Recruteur cherche candidats Data Scientist"
    )
    
    # Test 5: Recruteur - Recommandations avec job ID réel
    test_query(
        recruiter_id, 
        "recruiter",
        "Recommande-moi des candidats pour l'offre 11111111-aaaa-bbbb-cccc-111111111111", 
        "recherche_emploi",
        "Test 5: Recruteur - candidats pour Data Scientist Junior"
    )
    
    # Test 6: Recruteur - Recommandations pour OCR & Computer Vision
    test_query(
        recruiter_id, 
        "recruiter",
        "Trouve-moi des candidats pour l'offre 22222222-bbbb-cccc-dddd-222222222222", 
        "recherche_emploi",
        "Test 6: Recruteur - candidats pour OCR & Computer Vision"
    )
    
    # Test 7: Candidat - Recherche Frontend
    test_query(
        candidate_id, 
        "candidate",
        "Frontend React TypeScript", 
        "recherche_emploi",
        "Test 7: Candidat cherche Frontend React"
    )
    
    # Test 8: Candidat - Recherche UX Designer
    test_query(
        candidate_id, 
        "candidate",
        "UX Designer Figma", 
        "recherche_emploi",
        "Test 8: Candidat cherche UX Designer"
    )

if __name__ == "__main__":
    main()
