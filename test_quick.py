#!/usr/bin/env python3
"""
Test rapide du chatbot avec la nouvelle clé API
"""

import requests
import json
import base64

def test_chatbot():
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": "4db01413-00e5-4c1c-90f7-4c03438d1dc3", "role": "candidate"}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    # Test 1: Question simple
    print("🧪 Test 1: Question développeur Python")
    data = {
        "query": "Développeur Python",
        "pageContext": "recherche_emploi"
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=90)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès!")
            print(f"Réponse: {result['response'][:200]}...")
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test 2: Question fallback (CDI/CDD)
    print("🧪 Test 2: Question CDI/CDD (fallback)")
    data = {
        "query": "Quelle est la différence entre un CDI et un CDD ?",
        "pageContext": "contrats"
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=90)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès!")
            print(f"Réponse: {result['response'][:200]}...")
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")

if __name__ == "__main__":
    test_chatbot()
