const submitToFlaskAPI = async (formData: any) => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000);

  const headers = {
    "Content-Type": "application/json",
    "X-Recruiter-ID": user?.id || "3823eb09-f9f6-4bc7-a8f3-49e9aea76e1a",
    Accept: "application/json",
  };

  try {
    console.log(
      "Envoi des données à l'API Flask :",
      JSON.stringify(formData, null, 2)
    );

    const response = await fetch("http://localhost:5000/offer/create", {
      method: "POST",
      headers: headers,
      body: JSON.stringify(formData),
      signal: controller.signal,
      mode: "cors",
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error("Réponse d'erreur de l'API :", errorData);

      switch (response.status) {
        case 415:
          throw new Error(
            "Type de média non supporté : Assurez-vous que Content-Type est application/json"
          );
        case 401:
          throw new Error(
            "Échec de l'authentification : ID de recruteur manquant"
          );
        case 403:
          throw new Error(
            "Accès refusé : Vous n'avez pas la permission de créer des offres"
          );
        case 500:
          throw new Error(
            "Erreur serveur : Veuillez vérifier tous les champs requis"
          );
        default:
          throw new Error(`Erreur HTTP ! statut : ${response.status}`);
      }
    }

    const result = await response.json();
    console.log("Réponse de succès de l'API :", result);
    return result;
  } catch (error: any) {
    clearTimeout(timeoutId);
    console.error("Erreur de soumission :", error.message);

    if (error.name === "AbortError") {
      throw new Error(
        "Délai de requête dépassé : Le serveur Flask ne répond pas"
      );
    }

    if (error.message.includes("fetch")) {
      throw new Error(
        "Échec de la connexion : Assurez-vous que le serveur Flask est en cours d'exécution sur localhost:5000 avec CORS activé"
      );
    }

    throw error;
  }
};