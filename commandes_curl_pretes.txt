# Commandes curl prêtes à copier-coller pour tester ton chatbot

# ========================================
# CORRECTION DE TON SCRIPT BASH ORIGINAL
# ========================================

# Dans ton script, remplace cette ligne:
# USER_B64=$(echo -n "$USER_JSON" | base64)

# Par cette ligne:
# USER_B64=$(echo -n "$USER_JSON" | base64 | tr -d '\n')

# ========================================
# COMMANDES CURL INDIVIDUELLES
# ========================================

# Test 1: Candidat Mohammed Amine MAHID - Développeur Python
curl -X POST http://127.0.0.1:5000/chat/ \
  -H "Content-Type: application/json" \
  -H "X-User: eyJpZCI6ICI0ZGIwMTQxMy0wMGU1LTRjMWMtOTBmNy00YzAzNDM4ZDFkYzMiLCAicm9sZSI6ICJjYW5kaWRhdGUifQ==" \
  -d '{"query": "Developpeur Python", "pageContext": "recherche_emploi"}'

# Test 2: Candidat Mohammed Amine MAHID - Conseil CV
curl -X POST http://127.0.0.1:5000/chat/ \
  -H "Content-Type: application/json" \
  -H "X-User: eyJpZCI6ICI0ZGIwMTQxMy0wMGU1LTRjMWMtOTBmNy00YzAzNDM4ZDFkYzMiLCAicm9sZSI6ICJjYW5kaWRhdGUifQ==" \
  -d '{"query": "Comment ameliorer mon CV pour un poste de data scientist ?", "pageContext": "conseil"}'

# Test 3: Candidat Mohamed ouabbi - Frontend React
curl -X POST http://127.0.0.1:5000/chat/ \
  -H "Content-Type: application/json" \
  -H "X-User: eyJpZCI6ICJlZDE1ZjIwMC05YjE5LTRiZGUtODE5ZC1mM2I5YTI5YjM1ZTMiLCAicm9sZSI6ICJjYW5kaWRhdGUifQ==" \
  -d '{"query": "Frontend React", "pageContext": "recherche_emploi"}'

# Test 4: Recruteur - Profil développeur senior
curl -X POST http://127.0.0.1:5000/chat/ \
  -H "Content-Type: application/json" \
  -H "X-User: eyJpZCI6ICI2ZDJkZjk2YS1iNmIyLTQwOTgtYjVmMy0xMTkyZmJhNTQ0ZjciLCAicm9sZSI6ICJyZWNydWl0ZXIifQ==" \
  -d '{"query": "Profil developpeur senior", "pageContext": "recrutement"}'

# Test 5: Entretien développeur
curl -X POST http://127.0.0.1:5000/chat/ \
  -H "Content-Type: application/json" \
  -H "X-User: eyJpZCI6ICI0ZGIwMTQxMy0wMGU1LTRjMWMtOTBmNy00YzAzNDM4ZDFkYzMiLCAicm9sZSI6ICJjYW5kaWRhdGUifQ==" \
  -d '{"query": "Comment me preparer a un entretien developpeur backend ?", "pageContext": "entretien"}'

# ========================================
# TESTS D'ERREURS
# ========================================

# Test erreur 404: Utilisateur inexistant
curl -X POST http://127.0.0.1:5000/chat/ \
  -H "Content-Type: application/json" \
  -H "X-User: eyJpZCI6ICIwMDAwMDAwMC0wMDAwLTAwMDAtMDAwMC0wMDAwMDAwMDAwMDAiLCAicm9sZSI6ICJjYW5kaWRhdGUifQ==" \
  -d '{"query": "Test", "pageContext": "test"}'

# Test erreur 401: Sans header X-User
curl -X POST http://127.0.0.1:5000/chat/ \
  -H "Content-Type: application/json" \
  -d '{"query": "Test", "pageContext": "test"}'

# Test erreur 400: Header invalide
curl -X POST http://127.0.0.1:5000/chat/ \
  -H "Content-Type: application/json" \
  -H "X-User: invalid_base64_header" \
  -d '{"query": "Test", "pageContext": "test"}'

# ========================================
# SCRIPT BASH CORRIGÉ COMPLET
# ========================================

#!/bin/bash

URL="http://127.0.0.1:5000/chat/"
HEADER="Content-Type: application/json"

USER_JSON='{"id":"4db01413-00e5-4c1c-90f7-4c03438d1dc3","role":"candidate"}'
# CORRECTION: Ajouter | tr -d '\n'
USER_B64=$(echo -n "$USER_JSON" | base64 | tr -d '\n')

declare -a QUERIES=(
  '{"query": "Comment ameliorer mon CV pour un poste de data scientist ?", "pageContext": "conseil"}'
  '{"query": "Que dois-je mettre dans une lettre de motivation pour une startup tech ?", "pageContext": "lettre_motivation"}'
  '{"query": "Quelle est la difference entre un CDI et un CDD ?", "pageContext": "contrats"}'
  '{"query": "Comment me preparer a un entretien developpeur backend ?", "pageContext": "entretien"}'
  '{"query": "Est-ce qu il faut apprendre Docker pour trouver un job de developpeur ?", "pageContext": "skills"}'
)

for query in "${QUERIES[@]}"
do
  echo ">>> Question: $query"
  curl -s -X POST "$URL" \
    -H "$HEADER" \
    -H "X-User: $USER_B64" \
    -d "$query"
  echo -e "\n----------------------------"
done

# ========================================
# RÉSULTATS ATTENDUS
# ========================================

# Tu devrais maintenant voir des réponses qui commencent par:
# - "Bonjour Mohammed Amine MAHID !" pour le candidat 4db01413-00e5-4c1c-90f7-4c03438d1dc3
# - "Bonjour Mohamed ouabbi !" pour le candidat ed15f200-9b19-4bde-819d-f3b9a29b35e3
# - "Bonjour Utilisateur !" pour le recruteur (pas de nom dans la base)

# Les réponses sont personnalisées selon le rôle (candidat vs recruteur)
# et incluent des informations pertinentes sur les offres d'emploi disponibles.
