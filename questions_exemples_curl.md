# 🧪 Questions d'Exemple - Commandes Curl Prêtes

## 🚀 **Instructions**

1. **<PERSON><PERSON><PERSON><PERSON> votre serveur :** `python run.py`
2. **<PERSON><PERSON><PERSON>-collez** les commandes curl ci-dessous dans un nouveau terminal
3. **Analysez** les réponses pour voir l'analyse d'intention LLM en action

---

## 📋 **Questions par Catégorie**

### 1. **🤝 SALUTATIONS**

```bash
# Test: Bonjour simple
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Bonjour !","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

```bash
# Test: Salut familier
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Salut, comment ça va ?","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

---

### 2. **💼 RECHERCHE EMPLOI GÉNÉRALE**

```bash
# Test: Recherche générale
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Je cherche un travail","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

```bash
# Test: Liste des postes
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Quels sont les postes disponibles ?","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

---

### 3. **🔧 RECHERCHE PAR COMPÉTENCES**

```bash
# Test: Python
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Je cherche un poste en Python","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

```bash
# Test: React
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Avez-vous des offres pour développeur React ?","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

```bash
# Test: Data Science
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Postes data scientist","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

---

### 4. **📍 RECHERCHE PAR LOCALISATION**

```bash
# Test: Casablanca
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Jobs à Casablanca","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

```bash
# Test: Remote
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Postes en remote","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

---

### 5. **🎯 RECOMMANDATIONS PERSONNALISÉES**

```bash
# Test: Recommandations
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Recommandez-moi des offres","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

```bash
# Test: Profil match
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Quels postes me correspondent ?","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

---

### 6. **📈 CONSEILS RH**

```bash
# Test: Améliorer CV
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Comment améliorer mon CV ?","pageContext":"conseil","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

```bash
# Test: Conseils profil
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Conseils pour mon profil","pageContext":"conseil","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

---

### 7. **🎤 CONSEILS ENTRETIEN**

```bash
# Test: Préparation entretien
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Comment préparer un entretien ?","pageContext":"entretien","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

```bash
# Test: Entretien technique
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Conseils entretien technique","pageContext":"entretien","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

---

### 8. **🧠 QUESTIONS COMPLEXES**

```bash
# Test: Recherche complexe
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Je suis développeur Python avec 5 ans d'\''expérience, je cherche un poste remote en IA avec salaire 80k+","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

```bash
# Test: Reconversion
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Reconversion vers la data science, quelles compétences acquérir ?","pageContext":"conseil","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

---

### 9. **💰 RECHERCHE PAR SALAIRE**

```bash
# Test: Salaire minimum
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Postes avec salaire supérieur à 50000","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

```bash
# Test: Jobs bien payés
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Jobs bien payés","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

---

### 10. **👔 QUESTIONS RECRUTEUR**

```bash
# Test: Trouver candidats (avec utilisateur recruteur)
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Comment trouver de bons candidats ?","pageContext":"","role":"recruiter","user":{"id":"4db01413-00e5-4c1c-90f7-4c03438d1dc3","role":"recruiter"}}'
```

```bash
# Test: Profils Python (avec utilisateur recruteur)
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Profils Python disponibles","pageContext":"","role":"recruiter","user":{"id":"4db01413-00e5-4c1c-90f7-4c03438d1dc3","role":"recruiter"}}'
```

---

## 🎯 **Réponses Attendues**

### **Pour les Salutations :**
```json
{
  "response": "Bonjour Taoufiq ! Je suis votre assistant IA spécialisé dans la recherche d'emploi..."
}
```

### **Pour les Recherches d'Emploi :**
```json
{
  "response": "Bonjour Taoufiq ! Voici les offres d'emploi disponibles :\n\n**1. Data Scientist Junior**\n🏢 Entreprise: [Nom]\n📍 Lieu: Casablanca, Maroc\n💰 Salaire: 9000 - 13000 MAD\n🔧 Compétences: Python, SQL, Pandas..."
}
```

### **Pour les Conseils :**
```json
{
  "response": "Bonjour Taoufiq ! Voici mes conseils pour améliorer votre CV :\n\n✅ **Compétences techniques**\n- Mettez en avant vos compétences Python...\n\n✅ **Expérience**\n- Décrivez vos projets concrets..."
}
```

---

## 🔍 **Indicateurs de Qualité**

Vérifiez que chaque réponse contient :

- ✅ **Nom personnalisé** : "Taoufiq" ou "Mohammed Amine"
- ✅ **Emojis structurants** : 🏢, 📍, 💰, 🔧, 📝, ✅, 🎯, 💡
- ✅ **Format organisé** : **Gras**, listes, structure claire
- ✅ **Contenu pertinent** : Adapté au type de question
- ✅ **Appel à l'action** : Encouragement à poser d'autres questions

---

**🚀 Testez ces questions pour valider votre intégration LLM OpenRouter !**
