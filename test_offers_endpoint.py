#!/usr/bin/env python3
"""
Test script to verify the /offers endpoint
"""
import requests
import json
import time

def test_offers_endpoint():
    """Test the /offers GET endpoint"""
    print("🧪 Testing /offers endpoint...")
    
    # Wait a moment for server to be ready
    time.sleep(1)
    
    url = "http://localhost:5000/offers"
    
    # Headers from the fetch request
    headers = {
        "accept": "*/*",
        "accept-language": "fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7,ar;q=0.6,en-AU;q=0.5",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site"
    }
    
    try:
        print(f"📡 Making GET request to: {url}")
        print(f"📋 Headers: {json.dumps(headers, indent=2)}")
        
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: Offers endpoint responded successfully!")
            
            try:
                data = response.json()
                print(f"📄 Response Type: {type(data)}")
                
                if isinstance(data, list):
                    print(f"📊 Number of offers: {len(data)}")
                    if len(data) > 0:
                        print("📋 First offer structure:")
                        first_offer = data[0]
                        for key, value in first_offer.items():
                            print(f"   {key}: {type(value).__name__}")
                    else:
                        print("📭 No offers found in database")
                else:
                    print(f"📄 Response data: {data}")
                    
            except json.JSONDecodeError:
                print("⚠️  Response is not valid JSON")
                print(f"📄 Raw response: {response.text[:500]}...")
                
        else:
            print(f"❌ FAILED: HTTP {response.status_code}")
            print(f"📄 Response: {response.text}")
            
        return response.status_code == 200
        
    except requests.exceptions.ConnectionError:
        print("❌ FAILED: Could not connect to server")
        print("💡 Make sure the server is running with: python run.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ FAILED: Request timed out")
        return False
    except Exception as e:
        print(f"❌ FAILED: {str(e)}")
        return False

def test_offers_with_query_params():
    """Test the /offers endpoint with query parameters"""
    print("\n🔍 Testing /offers with query parameters...")
    
    # Test with empty query string (like in the original fetch)
    url = "http://localhost:5000/offers?"
    
    try:
        response = requests.get(url, timeout=5)
        print(f"📡 GET {url}")
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Query parameter test passed")
        else:
            print(f"❌ Query parameter test failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Query parameter test error: {str(e)}")

def test_cors_headers():
    """Test CORS headers"""
    print("\n🌐 Testing CORS headers...")
    
    url = "http://localhost:5000/offers"
    headers = {
        "Origin": "http://localhost:3000",
        "Access-Control-Request-Method": "GET"
    }
    
    try:
        # Test preflight request
        response = requests.options(url, headers=headers, timeout=5)
        print(f"📡 OPTIONS {url}")
        print(f"📊 Status: {response.status_code}")
        print(f"📋 CORS Headers:")
        for header, value in response.headers.items():
            if 'access-control' in header.lower():
                print(f"   {header}: {value}")
                
    except Exception as e:
        print(f"❌ CORS test error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Testing /offers Endpoint")
    print("=" * 60)
    
    success = test_offers_endpoint()
    test_offers_with_query_params()
    test_cors_headers()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 /offers endpoint is working correctly!")
        print("💡 The fetch request should work from the frontend")
    else:
        print("⚠️  /offers endpoint has issues")
        print("🔧 Check server logs for more details")
    print("=" * 60)
