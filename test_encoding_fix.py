#!/usr/bin/env python3
"""
Test des queries qui avaient des problèmes d'encodage
"""

import requests
import json
import base64

def test_query(query, page_context, description):
    """Test une query spécifique"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": "4db01413-00e5-4c1c-90f7-4c03438d1dc3", "role": "candidate"}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"🧪 {description}")
    print(f"Query: {query}")
    print(f"Context: {page_context}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès!")
            response_text = result['response']
            if len(response_text) > 300:
                print(f"Réponse: {response_text[:300]}...")
                print(f"[Réponse complète: {len(response_text)} caractères]")
            else:
                print(f"Réponse: {response_text}")
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")
    
    print("\n" + "="*60 + "\n")

def main():
    print("🔧 Test des corrections d'encodage")
    print("="*60)
    
    # Test des queries qui avaient des problèmes
    test_query(
        "Comment me préparer pour un entretien technique ?", 
        "entretien",
        "Test 1: Préparation entretien (était en erreur)"
    )
    
    test_query(
        "Quelles sont les compétences les plus demandées en 2024 ?", 
        "skills",
        "Test 2: Compétences 2024 (était en erreur)"
    )
    
    # Test supplémentaire avec des caractères spéciaux
    test_query(
        "Développeur Full-Stack JavaScript/TypeScript", 
        "recherche_emploi",
        "Test 3: Recherche avec caractères spéciaux"
    )

if __name__ == "__main__":
    main()
