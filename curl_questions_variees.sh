#!/bin/bash

# Collection de commandes curl pour tester differents types de questions
# Basees sur votre format exact

BASE_URL="http://localhost:5000/chat/"
USER_CANDIDATE='{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}'
USER_RECRUITER='{"id":"4db01413-00e5-4c1c-90f7-4c03438d1dc3","role":"recruiter"}'

echo "🚀 Tests de Questions Variees avec Curl"
echo "======================================"

# 1. SALUTATIONS
echo -e "\n📋 1. SALUTATIONS"
echo "=================="

echo -e "\n🔍 Test: Bonjour simple"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Bonjour !\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

echo -e "\n🔍 Test: Salut familier"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Salut, comment ca va ?\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

# 2. RECHERCHE EMPLOI GENERALE
echo -e "\n📋 2. RECHERCHE EMPLOI GENERALE"
echo "==============================="

echo -e "\n🔍 Test: Recherche generale"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Je cherche un travail\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

echo -e "\n🔍 Test: Liste des postes"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Quels sont les postes disponibles ?\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

# 3. RECHERCHE PAR COMPETENCES
echo -e "\n📋 3. RECHERCHE PAR COMPETENCES"
echo "==============================="

echo -e "\n🔍 Test: Python"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Je cherche un poste en Python\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

echo -e "\n🔍 Test: React"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Avez-vous des offres pour developpeur React ?\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

echo -e "\n🔍 Test: Data Science"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Postes data scientist\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

# 4. RECHERCHE PAR LOCALISATION
echo -e "\n📋 4. RECHERCHE PAR LOCALISATION"
echo "================================"

echo -e "\n🔍 Test: Casablanca"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Jobs a Casablanca\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

echo -e "\n🔍 Test: Remote"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Postes en remote\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

# 5. RECOMMANDATIONS PERSONNALISEES
echo -e "\n📋 5. RECOMMANDATIONS PERSONNALISEES"
echo "===================================="

echo -e "\n🔍 Test: Recommandations"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Recommandez-moi des offres\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

echo -e "\n🔍 Test: Profil match"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Quels postes me correspondent ?\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

# 6. CONSEILS RH
echo -e "\n📋 6. CONSEILS RH"
echo "================"

echo -e "\n🔍 Test: Ameliorer CV"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Comment ameliorer mon CV ?\",\"pageContext\":\"conseil\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

echo -e "\n🔍 Test: Conseils profil"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Conseils pour mon profil\",\"pageContext\":\"conseil\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

# 7. CONSEILS ENTRETIEN
echo -e "\n📋 7. CONSEILS ENTRETIEN"
echo "======================="

echo -e "\n🔍 Test: Preparation entretien"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Comment preparer un entretien ?\",\"pageContext\":\"entretien\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

echo -e "\n🔍 Test: Entretien technique"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Conseils entretien technique\",\"pageContext\":\"entretien\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

# 8. QUESTIONS COMPLEXES
echo -e "\n📋 8. QUESTIONS COMPLEXES"
echo "========================="

echo -e "\n🔍 Test: Recherche complexe"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Je suis developpeur Python avec 5 ans d'experience, je cherche un poste remote en IA avec salaire 80k+\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

echo -e "\n🔍 Test: Reconversion"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Reconversion vers la data science, quelles competences acquerir ?\",\"pageContext\":\"conseil\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

# 9. QUESTIONS RECRUTEUR
echo -e "\n📋 9. QUESTIONS RECRUTEUR"
echo "========================="

echo -e "\n🔍 Test: Trouver candidats"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Comment trouver de bons candidats ?\",\"pageContext\":\"\",\"role\":\"recruiter\",\"user\":$USER_RECRUITER}" \
  -w "\nStatus: %{http_code}\n\n"

echo -e "\n🔍 Test: Profils Python"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Profils Python disponibles\",\"pageContext\":\"\",\"role\":\"recruiter\",\"user\":$USER_RECRUITER}" \
  -w "\nStatus: %{http_code}\n\n"

# 10. RECHERCHE PAR SALAIRE
echo -e "\n📋 10. RECHERCHE PAR SALAIRE"
echo "============================"

echo -e "\n🔍 Test: Salaire minimum"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Postes avec salaire superieur a 50000\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

echo -e "\n🔍 Test: Jobs bien payes"
curl "$BASE_URL" \
  -H 'Content-Type: application/json' \
  -d "{\"query\":\"Jobs bien payes\",\"pageContext\":\"\",\"role\":\"candidate\",\"user\":$USER_CANDIDATE}" \
  -w "\nStatus: %{http_code}\n\n"

echo -e "\n✅ Tests termines!"
echo "=================="
echo "📊 Tous les types de questions ont ete testes"
echo "🤖 Verifiez que l'analyse d'intention LLM fonctionne correctement"
echo "🎯 Les reponses doivent etre personnalisees selon le type de question"
