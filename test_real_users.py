#!/usr/bin/env python3
"""
Test du chatbot avec les vrais IDs utilisateur
"""

import requests
import json
import base64

def test_user(user_id, role, query, page_context, description):
    """Test avec un utilisateur spécifique"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": user_id, "role": role}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"🧪 {description}")
    print(f"User ID: {user_id[:8]}... (role: {role})")
    print(f"Query: {query}")
    print(f"Context: {page_context}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès!")
            response_text = result['response']
            
            # Extraire le nom d'utilisateur de la réponse
            if "Bonjour" in response_text:
                greeting_line = response_text.split('\n')[0]
                print(f"👋 Salutation: {greeting_line}")
            
            if len(response_text) > 300:
                print(f"📝 Réponse: {response_text[:300]}...")
                print(f"[Réponse complète: {len(response_text)} caractères]")
            else:
                print(f"📝 Réponse: {response_text}")
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")
    
    print("\n" + "="*70 + "\n")

def main():
    print("👥 Test du chatbot avec les vrais utilisateurs")
    print("="*70)
    
    # IDs fournis
    recruiter_id = "e714bb4b-b3f8-4343-8d5d-1e120e421b29"
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"
    
    # Test 1: Candidat - Recherche d'emploi
    test_user(
        candidate_id, 
        "candidate",
        "Développeur Python Django", 
        "recherche_emploi",
        "Test 1: Candidat cherche emploi Python"
    )
    
    # Test 2: Recruteur - Même recherche
    test_user(
        recruiter_id, 
        "recruiter",
        "Développeur Python Django", 
        "recherche_emploi",
        "Test 2: Recruteur cherche profil Python"
    )
    
    # Test 3: Candidat - Conseil CV
    test_user(
        candidate_id, 
        "candidate",
        "Comment améliorer mon CV pour un poste de développeur ?", 
        "conseil",
        "Test 3: Candidat demande conseil CV"
    )
    
    # Test 4: Recruteur - Conseil recrutement
    test_user(
        recruiter_id, 
        "recruiter",
        "Comment évaluer les compétences techniques d'un candidat ?", 
        "conseil",
        "Test 4: Recruteur demande conseil évaluation"
    )
    
    # Test 5: Candidat - Préparation entretien
    test_user(
        candidate_id, 
        "candidate",
        "Comment me préparer pour un entretien développeur ?", 
        "entretien",
        "Test 5: Candidat prépare entretien"
    )
    
    # Test 6: Recruteur - Conduite entretien
    test_user(
        recruiter_id, 
        "recruiter",
        "Quelles questions poser lors d'un entretien technique ?", 
        "entretien",
        "Test 6: Recruteur conduit entretien"
    )
    
    # Test 7: Candidat - Question contrats
    test_user(
        candidate_id, 
        "candidate",
        "Quelle est la différence entre CDI et CDD ?", 
        "contrats",
        "Test 7: Candidat s'informe sur contrats"
    )
    
    # Test 8: Recruteur - Question contrats
    test_user(
        recruiter_id, 
        "recruiter",
        "Quand proposer un CDD plutôt qu'un CDI ?", 
        "contrats",
        "Test 8: Recruteur choisit type contrat"
    )

if __name__ == "__main__":
    main()
