# Documentation - Fonction recommendation_candidate_rh

## Vue d'ensemble

La fonction `recommendation_candidate_rh` analyse le profil d'un candidat et les offres d'emploi disponibles pour fournir des recommandations personnalisées d'amélioration du score de matching.

## Fonctionnalités

### 🎯 Analyse complète du profil
- Récupère toutes les compétences du candidat (skillner_skills, py_skills, added_skills)
- Analyse l'expérience, l'éducation et la localisation
- Compare avec un échantillon d'offres d'emploi actives

### 📊 Recommandations personnalisées
- **Points forts** : Compétences déjà possédées et demandées sur le marché
- **Compétences à développer** : Top 5 des compétences manquantes les plus demandées
- **Conseils d'apprentissage** : Suggestions concrètes pour acquérir de nouvelles compétences
- **Opportunités géographiques** : Analyse des localisations avec le plus d'opportunités

### 📈 Métriques d'impact
- Taux de correspondance actuel
- Amélioration potentielle estimée
- Nombre total de compétences analysées

## Utilisation

### Via l'endpoint chat principal

```bash
POST /api/chat/
```

**Body:**
```json
{
  "query": "Comment améliorer mon profil ?",
  "pageContext": "conseil",
  "user": {
    "id": "candidate-uuid",
    "role": "candidate"
  }
}
```

### Queries détectées automatiquement

La fonction est automatiquement déclenchée quand la query contient :

**Mots-clés:**
- améliorer, amélioration, conseil, conseils
- augmenter, score, matching, profil, compétences
- développer, manque, formation, apprendre
- aide, progresser, évoluer, carrière

**Phrases typiques:**
- "Comment améliorer mon profil ?"
- "Quelles compétences dois-je développer ?"
- "Conseil pour augmenter mon score"
- "Aide moi à progresser"

## Exemple de réponse

```
Bonjour Jean Dupont ! 🎯

📊 ANALYSE DE VOTRE PROFIL ET RECOMMANDATIONS

✅ VOS POINTS FORTS :
1. Python (demandée dans 15 offres)
2. JavaScript (demandée dans 12 offres)
3. React (demandée dans 8 offres)

🚀 COMPÉTENCES À DÉVELOPPER PRIORITAIREMENT :
1. Docker (demandée dans 18 offres)
2. AWS (demandée dans 16 offres)
3. Kubernetes (demandée dans 14 offres)
4. Node.js (demandée dans 12 offres)
5. TypeScript (demandée dans 10 offres)

💡 CONSEILS D'APPRENTISSAGE :
- Commencez par les 2-3 premières compétences listées
- Suivez des formations en ligne (Coursera, Udemy, OpenClassrooms)
- Pratiquez sur des projets personnels
- Obtenez des certifications reconnues

📍 OPPORTUNITÉS GÉOGRAPHIQUES :
Votre localisation actuelle : Paris
Localisations avec le plus d'opportunités :
1. Paris (25 offres)
2. Lyon (12 offres)
3. Toulouse (8 offres)

📈 IMPACT POTENTIEL :
- Taux de correspondance actuel : 35.2%
- Amélioration potentielle : +18.5%
- Nombre total de compétences analysées : 54

🎯 PROCHAINES ÉTAPES :
1. Mettez à jour votre profil avec les nouvelles compétences acquises
2. Ajoutez des projets démontrant ces compétences
3. Consultez régulièrement les nouvelles offres
4. Postulez aux offres correspondant à vos compétences actuelles

💪 Ces recommandations sont basées sur l'analyse de 20 offres d'emploi récentes. Bonne chance dans votre recherche !
```

## Sécurité

- ✅ Authentification requise (candidat uniquement)
- ✅ Vérification de l'existence du candidat en base
- ✅ Gestion d'erreurs complète
- ✅ Logs détaillés pour le debugging

## Limitations

- Analyse basée sur un échantillon de 20 offres d'emploi
- Nécessite un profil candidat complet pour des résultats optimaux
- Les recommandations sont génériques et peuvent nécessiter une personnalisation supplémentaire

## Tests

Utilisez le script `test_rh_recommendation.py` pour tester la fonctionnalité :

```bash
python test_rh_recommendation.py
```

## Intégration Frontend

### React/JavaScript

```javascript
// Avec authentification header
const getRHRecommendations = async () => {
  const userData = { id: candidateId, role: 'candidate' };
  const userDataB64 = btoa(JSON.stringify(userData));
  
  const response = await fetch('/api/chat/recommendation-rh', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-User': userDataB64
    },
    body: JSON.stringify({})
  });
  
  const data = await response.json();
  return data.response;
};

// Ou via chat avec query
const getChatRecommendations = async (query) => {
  const response = await fetch('/api/chat/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-User': userDataB64
    },
    body: JSON.stringify({
      query: query,
      pageContext: 'conseil'
    })
  });
  
  const data = await response.json();
  return data.response;
};
```

## Maintenance

- Surveiller les logs pour les erreurs
- Mettre à jour les mots-clés de détection si nécessaire
- Ajuster l'échantillon d'offres analysées selon les besoins
- Optimiser les requêtes base de données si performance dégradée
