#!/usr/bin/env python3
"""
Test interactif pour l'endpoint /chat/
"""

import requests
import json

# IDs réels de ta base
REAL_CANDIDATE_ID = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"
REAL_RECRUITER_ID = "6d2df96a-b6b2-4098-b5f3-1192fba544f7"

def test_chat(user_id, role, query):
    """Test l'endpoint chat avec les paramètres donnés"""
    url = "http://127.0.0.1:5000/chat/"
    
    headers = {
        "Content-Type": "application/json",
        "X-User": json.dumps({"id": user_id, "role": role})
    }
    
    data = {
        "query": query,
        "pageContext": "homepage"
    }
    
    try:
        print(f"🔄 Test en cours...")
        print(f"   User: {user_id[:8]}... (role: {role})")
        print(f"   Query: {query}")
        
        response = requests.post(url, headers=headers, json=data)
        
        print(f"\n📊 Résultat:")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Succès!")
            print(f"   💬 Réponse: {result['response'][:150]}...")
        else:
            error = response.json()
            print(f"   ❌ Erreur: {error}")
            
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    print("\n" + "="*60 + "\n")

def main():
    print("🤖 Test interactif du chatbot")
    print("="*60)
    
    while True:
        print("\nChoisissez un test :")
        print("1. Test candidat réel")
        print("2. Test recruteur réel") 
        print("3. Test candidat inexistant (erreur)")
        print("4. Test sans header (erreur)")
        print("5. Test personnalisé")
        print("6. Quitter")
        
        choice = input("\nVotre choix (1-6): ").strip()
        
        if choice == "1":
            query = input("Query (ou Enter pour 'Développeur'): ").strip() or "Développeur"
            test_chat(REAL_CANDIDATE_ID, "candidate", query)
            
        elif choice == "2":
            query = input("Query (ou Enter pour 'Frontend Developer'): ").strip() or "Frontend Developer"
            test_chat(REAL_RECRUITER_ID, "recruiter", query)
            
        elif choice == "3":
            query = input("Query (ou Enter pour 'Test'): ").strip() or "Test"
            test_chat("00000000-0000-0000-0000-000000000000", "candidate", query)
            
        elif choice == "4":
            print("🔄 Test sans header X-User...")
            url = "http://127.0.0.1:5000/chat/"
            headers = {"Content-Type": "application/json"}
            data = {"query": "Test", "pageContext": "homepage"}
            
            try:
                response = requests.post(url, headers=headers, json=data)
                print(f"📊 Status: {response.status_code}")
                print(f"❌ Erreur: {response.json()}")
            except Exception as e:
                print(f"💥 Exception: {e}")
            print("\n" + "="*60 + "\n")
            
        elif choice == "5":
            user_id = input("User ID: ").strip()
            role = input("Role (candidate/recruiter): ").strip()
            query = input("Query: ").strip()
            
            if user_id and role and query:
                test_chat(user_id, role, query)
            else:
                print("❌ Tous les champs sont requis!")
                
        elif choice == "6":
            print("👋 Au revoir!")
            break
            
        else:
            print("❌ Choix invalide!")

if __name__ == "__main__":
    main()
