#!/usr/bin/env python3
"""
Test simple pour verifier que le serveur demarre correctement
"""

import sys
import os

# Ajouter le repertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test que tous les imports fonctionnent"""
    try:
        print("🧪 Test des imports...")
        
        # Test import de l'app
        from app import create_app
        print("✅ Import create_app: OK")
        
        # Test import du service LLM
        from app.services.llm_service import get_openrouter_llm_service
        print("✅ Import LLM service: OK")
        
        # Test import des routes chat
        from app.routes.chat import rag_bp
        print("✅ Import chat routes: OK")
        
        # Test creation de l'app
        app = create_app()
        print("✅ Creation de l'app: OK")
        
        print("\n🎉 Tous les imports fonctionnent correctement!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'import: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_llm_service():
    """Test que le service LLM peut etre instancie"""
    try:
        print("\n🤖 Test du service LLM...")
        
        from app.services.llm_service import get_openrouter_llm_service
        
        # Instancier le service
        llm_service = get_openrouter_llm_service()
        print("✅ Instanciation du service LLM: OK")
        
        # Test de l'analyse d'intention (fallback)
        intent = llm_service._fallback_intent_analysis("bonjour", "", "candidate")
        print(f"✅ Analyse d'intention fallback: {intent['service']}")
        
        print("\n🎉 Service LLM fonctionne correctement!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur service LLM: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("🚀 Test de demarrage du serveur")
    print("=" * 50)
    
    # Test des imports
    imports_ok = test_imports()
    
    # Test du service LLM
    llm_ok = test_llm_service()
    
    # Resultat final
    print("\n" + "=" * 50)
    print("📊 RESULTAT FINAL")
    print("=" * 50)
    
    if imports_ok and llm_ok:
        print("✅ Tous les tests passent!")
        print("🚀 Le serveur peut demarrer avec: python run.py")
        return True
    else:
        print("❌ Certains tests echouent")
        print("🔧 Verifiez les erreurs ci-dessus")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
