# 🔧 Correction de ton script bash

## ❌ Problème dans ton script original :

```bash
USER_B64=$(echo -n "$USER_JSON" | base64)
```

## ✅ Solution - Remplace cette ligne par :

```bash
USER_B64=$(echo -n "$USER_JSON" | base64 | tr -d '\n')
```

## 🔍 Explication :

- **Problème** : La commande `base64` sous Git Bash/MinGW peut ajouter des retours à la ligne
- **Solution** : `tr -d '\n'` supprime tous les retours à la ligne
- **Alternative** : `base64 -w 0` (si supporté par ta version de base64)

## 📝 Script bash complet corrigé :

```bash
#!/bin/bash

URL="http://127.0.0.1:5000/chat/"
HEADER="Content-Type: application/json"

USER_JSON='{"id":"4db01413-00e5-4c1c-90f7-4c03438d1dc3","role":"candidate"}'
# CORRECTION ICI ⬇️
USER_B64=$(echo -n "$USER_JSON" | base64 | tr -d '\n')

declare -a QUERIES=(
  '{"query": "Comment améliorer mon CV pour un poste de data scientist ?", "pageContext": "conseil"}'
  '{"query": "Que dois-je mettre dans une lettre de motivation pour une startup tech ?", "pageContext": "lettre_motivation"}'
  '{"query": "Quelle est la différence entre un CDI et un CDD ?", "pageContext": "contrats"}'
  '{"query": "Comment me préparer à un entretien développeur backend ?", "pageContext": "entretien"}'
  '{"query": "Est-ce qu'il faut apprendre Docker pour trouver un job de développeur ?", "pageContext": "skills"}'
)

for query in "${QUERIES[@]}"
do
  echo ">>> Question: $query"
  curl -s -X POST "$URL" \
    -H "$HEADER" \
    -H "X-User: $USER_B64" \
    -d "$query"
  echo -e "\n----------------------------"
done
```

## 🧪 Test rapide :

Pour vérifier que ton encodage base64 est correct :

```bash
USER_JSON='{"id":"ton-id-ici","role":"candidate"}'
USER_B64=$(echo -n "$USER_JSON" | base64 | tr -d '\n')
echo "Encodé: $USER_B64"

# Test de décodage
echo "$USER_B64" | base64 -d
```

## 🎯 ID valides à utiliser :

D'après ta base de données :
- **Candidat** : `4db01413-00e5-4c1c-90f7-4c03438d1dc3`
- **Recruteur** : `6d2df96a-b6b2-4098-b5f3-1192fba544f7`

## 🚀 Commande de test rapide :

```bash
curl -X POST http://127.0.0.1:5000/chat/ \
  -H "Content-Type: application/json" \
  -H "X-User: eyJpZCI6ICI0ZGIwMTQxMy0wMGU1LTRjMWMtOTBmNy00YzAzNDM4ZDFkYzMiLCAicm9sZSI6ICJjYW5kaWRhdGUifQ==" \
  -d '{"query": "Développeur Python", "pageContext": "recherche_emploi"}'
```
