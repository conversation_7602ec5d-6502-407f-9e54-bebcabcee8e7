# 🧪 Test Manuel de votre Commande Curl

## 🚀 **Étapes à suivre**

### 1. **<PERSON><PERSON>marrer le serveur Flask**

Ouvrez un terminal et exécutez :

```bash
cd C:\Users\<USER>\Desktop\backend\backendV1
python run.py
```

**⏳ Attendez de voir ces messages :**
```
loading full_matcher ...
loading abv_matcher ...
loading full_uni_matcher ...
loading low_form_matcher ...
loading token_matcher ...
 * Running on http://127.0.0.1:5000
 * Debug mode: on
```

### 2. **Tester avec votre curl exact**

Dans un **nouveau terminal**, exécutez votre commande curl exacte :

```bash
curl 'http://localhost:5000/chat/' \
  -H 'Accept: application/json' \
  -H 'Accept-Language: fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -H 'Origin: http://localhost:3000' \
  -H 'Referer: http://localhost:3000/' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-site' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  --data-raw '{"query":"donner moi les poste   *","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

### 3. **Version simplifiée (si curl pose problème)**

```bash
curl -X POST http://localhost:5000/chat/ \
  -H "Content-Type: application/json" \
  -d '{"query":"donner moi les poste   *","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

### 4. **Test avec Python (alternative)**

```bash
python test_direct_curl.py
```

## 🎯 **Réponse Attendue**

Vous devriez recevoir une réponse JSON comme :

```json
{
  "response": "Bonjour Taoufiq ! Voici les offres d'emploi disponibles :\n\n**1. Data Scientist Junior**\n🏢 Entreprise: [Nom entreprise]\n📍 Lieu: Casablanca, Maroc\n💰 Salaire: 9000 - 13000 MAD\n🔧 Compétences: Python, SQL, Pandas, Scikit-learn, Power BI\n📝 Description: Nous recherchons un data scientist junior pour développer des modèles prédictifs...\n\n**2. Développeur OCR & Computer Vision**\n🏢 Entreprise: [Nom entreprise]\n📍 Lieu: Rabat, Maroc\n💰 Salaire: 10000 - 15000 MAD\n🔧 Compétences: Python, TensorFlow, YOLO, OpenCV, PyQt5\n📝 Description: Intégrez notre équipe pour développer des solutions OCR intelligentes...\n\n**3. Senior Software Engineer (Full Stack)**\n🏢 Entreprise: [Nom entreprise]\n📍 Lieu: casablanca\n💰 Salaire: 10000 - 15000 MAD\n🔧 Compétences: Spring Boot, ASP.NET, React.js\n📝 Description: We are seeking a highly skilled and experienced Senior Software Engineer...\n\n💡 Ces offres correspondent à votre recherche. N'hésitez pas à me poser des questions plus spécifiques ou à demander des conseils pour postuler !"
}
```

## ✅ **Indicateurs de Succès**

Votre réponse devrait contenir :

- ✅ **Salutation personnalisée** : "Bonjour Taoufiq !"
- ✅ **Offres structurées** avec emojis (🏢, 📍, 💰, 🔧)
- ✅ **Données réelles** de votre base de données :
  - Data Scientist Junior (Casablanca)
  - Développeur OCR & Computer Vision (Rabat)
  - Senior Software Engineer (Full Stack)
  - Senior Frontend Developer (Paris, Remote)
  - Senior Backend Engineer (Remote)
- ✅ **Appel à l'action** encourageant
- ✅ **Format professionnel** et engageant

## 🛠️ **Dépannage**

### **Erreur : Connection refused**
```
❌ Le serveur n'est pas démarré
💡 Solution : python run.py et attendez 2-3 minutes
```

### **Erreur 500 : Internal Server Error**
```
❌ Problème avec la base de données ou les modèles
💡 Vérifiez les logs du serveur Flask
💡 Vérifiez que Supabase est accessible
```

### **Réponse sans LLM (basique)**
```
❌ OpenRouter LLM non disponible
✅ Le système utilise le fallback automatique
💡 Vérifiez OPENROUTER_API_KEY dans .env
```

### **Chargement très lent**
```
⏳ Premier démarrage : 2-3 minutes (chargement des modèles AI)
⚡ Requêtes suivantes : < 5 secondes
```

## 📊 **Logs à Surveiller**

Dans le terminal du serveur Flask, vous devriez voir :

```
[LLM] Appel OpenRouter avec modele: anthropic/claude-3.5-sonnet
[LLM] Reponse recue: 1234 caracteres
[LLM] Intention analysee: rag_search (confiance: 0.8)
```

## 🎉 **Test Réussi !**

Si vous voyez une réponse personnalisée avec le nom "Taoufiq" et des offres d'emploi structurées, votre intégration LLM OpenRouter fonctionne parfaitement !

---

**🚀 Votre chatbot est maintenant équipé d'une IA de pointe !**
