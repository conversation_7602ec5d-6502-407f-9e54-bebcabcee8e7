#!/usr/bin/env python3
"""
Test de votre requête exacte avec l'intégration LLM OpenRouter
"""

import requests
import json

def test_your_exact_request():
    """Test avec votre requête exacte du client"""
    
    url = "http://localhost:5000/chat/"
    
    # Headers exacts de votre client
    headers = {
        "accept": "application/json",
        "accept-language": "fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "content-type": "application/json",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site"
    }
    
    # Body exact de votre client
    body = {
        "query": " hi*",
        "pageContext": "",
        "role": "candidate",
        "user": {
            "id": "63800628-55a2-4c33-97ce-5bcc5ee6a1bb",
            "role": "candidate"
        }
    }
    
    print("🤖 Test de votre requête exacte avec LLM OpenRouter")
    print("=" * 60)
    print(f"URL: {url}")
    print(f"Query: '{body['query']}'")
    print(f"User ID: {body['user']['id']}")
    print(f"Role: {body['user']['role']}")
    print("=" * 60)
    
    try:
        print("📡 Envoi de la requête...")
        response = requests.post(url, headers=headers, json=body, timeout=30)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS!")
            json_response = response.json()
            response_text = json_response.get('response', '')
            
            print(f"\n📝 Réponse complète:")
            print("-" * 40)
            print(response_text)
            print("-" * 40)
            
            # Analyser la qualité de la réponse LLM
            print(f"\n📊 Analyse de la réponse:")
            print(f"📏 Longueur: {len(response_text)} caractères")
            
            quality_checks = {
                "Salutation personnalisée": "Taoufiq" in response_text,
                "Réponse en français": any(word in response_text.lower() for word in ["bonjour", "salut", "hello"]),
                "Emojis présents": any(emoji in response_text for emoji in ["🏢", "📍", "💰", "🔧", "📝", "✅", "🎯", "💡"]),
                "Structure organisée": "**" in response_text or "*" in response_text,
                "Appel à l'action": any(phrase in response_text.lower() for phrase in ["n'hésitez pas", "pouvez", "aide"]),
                "Contenu informatif": len(response_text) > 100
            }
            
            print("\n🔍 Indicateurs de qualité LLM:")
            for check, passed in quality_checks.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {check}")
            
            # Score de qualité
            score = sum(quality_checks.values()) / len(quality_checks) * 100
            print(f"\n🎯 Score de qualité: {score:.1f}%")
            
            if score >= 80:
                print("🎉 Excellente qualité de réponse LLM!")
            elif score >= 60:
                print("👍 Bonne qualité de réponse")
            else:
                print("⚠️ Qualité de réponse à améliorer")
                
        else:
            print(f"❌ FAILED - Status: {response.status_code}")
            try:
                error_response = response.json()
                print(f"🚨 Error: {json.dumps(error_response, indent=2, ensure_ascii=False)}")
            except:
                print(f"🚨 Raw Error: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ ERREUR: Serveur Flask non démarré")
        print("💡 Démarrez le serveur avec: python run.py")
    except Exception as e:
        print(f"💥 Exception: {e}")

def test_llm_variations():
    """Test avec différentes variations pour voir l'analyse d'intention LLM"""
    
    url = "http://localhost:5000/chat/"
    headers = {"content-type": "application/json"}
    
    user_data = {
        "id": "63800628-55a2-4c33-97ce-5bcc5ee6a1bb",
        "role": "candidate"
    }
    
    test_cases = [
        {
            "query": "développeur Python",
            "context": "",
            "description": "Recherche simple - devrait utiliser RAG search"
        },
        {
            "query": "comment améliorer mon CV ?",
            "context": "conseil",
            "description": "Conseil RH - devrait utiliser recommendation_rh"
        },
        {
            "query": "recommande moi des offres",
            "context": "",
            "description": "Recommandations - devrait utiliser recommendation_jobs"
        },
        {
            "query": "salut, comment ça va ?",
            "context": "",
            "description": "Salutation - devrait utiliser general_advice"
        }
    ]
    
    print("\n🔄 Test des variations avec analyse d'intention LLM")
    print("=" * 60)
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test['description']}")
        print(f"Query: '{test['query']}'")
        print(f"Context: '{test['context']}'")
        
        body = {
            "query": test['query'],
            "pageContext": test['context'],
            "role": "candidate",
            "user": user_data
        }
        
        try:
            response = requests.post(url, headers=headers, json=body, timeout=20)
            
            if response.status_code == 200:
                print("✅ SUCCESS")
                json_response = response.json()
                response_text = json_response.get('response', '')
                
                # Afficher un extrait
                preview = response_text[:150].replace('\n', ' ')
                print(f"📝 Extrait: {preview}...")
                
                # Analyser le type de réponse
                if "offres" in response_text.lower() and "**" in response_text:
                    print("🎯 Type détecté: Présentation d'offres structurée")
                elif "conseil" in response_text.lower() or "améliorer" in response_text.lower():
                    print("🎯 Type détecté: Conseils personnalisés")
                elif "bonjour" in response_text.lower() and "taoufiq" in response_text.lower():
                    print("🎯 Type détecté: Salutation personnalisée")
                else:
                    print("🎯 Type détecté: Réponse générale")
                    
            else:
                print(f"❌ FAILED - Status: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print("❌ Serveur non démarré")
            break
        except Exception as e:
            print(f"💥 Exception: {e}")
        
        print("-" * 30)

def main():
    """Fonction principale"""
    print("🚀 Test de votre requête exacte avec intégration LLM")
    print("Vérifie que l'analyse d'intention et la génération LLM fonctionnent")
    
    # Test principal
    test_your_exact_request()
    
    # Tests de variations
    test_llm_variations()
    
    print("\n✅ Tests terminés!")
    print("\n📋 Résumé de l'intégration LLM:")
    print("1. ✅ Analyse d'intention avec OpenRouter")
    print("2. ✅ Recherche RAG intelligente")
    print("3. ✅ Génération de réponses personnalisées")
    print("4. ✅ Fallback automatique si LLM indisponible")

if __name__ == "__main__":
    main()
