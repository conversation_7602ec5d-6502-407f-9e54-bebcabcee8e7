#!/usr/bin/env python3
"""
Test simple pour vérifier la logique de validation utilisateur
"""

import requests
import json

def simple_test():
    url = "http://127.0.0.1:5000/chat/"
    
    # Test avec un utilisateur inexistant (doit échouer)
    headers = {
        "Content-Type": "application/json",
        "X-User": json.dumps({"id": "fake_user_123", "role": "candidate"})
    }
    
    data = {
        "query": "Développeur",
        "pageContext": "homepage"
    }
    
    print("=== Test: Utilisateur inexistant ===")
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Erreur: {e}")

if __name__ == "__main__":
    simple_test()
