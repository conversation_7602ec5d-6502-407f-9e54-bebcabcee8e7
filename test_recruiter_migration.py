#!/usr/bin/env python3
"""
Script pour tester et exécuter la migration des recruiter_id
"""
import os
from supabase import create_client
from dotenv import load_dotenv

load_dotenv()

def test_migration():
    """Test et exécution de la migration recruiter_id"""
    
    # Connexion Supabase
    supabase = create_client(
        os.getenv("SUPABASE_URL"),
        os.getenv("SUPABASE_KEY")
    )
    
    print("🔍 AVANT MIGRATION")
    print("=" * 50)
    
    # 1. État actuel
    try:
        jobs = supabase.table("jobs").select("id, title, recruiter_id, company_id").execute()
        total_jobs = len(jobs.data)
        jobs_with_recruiter = len([j for j in jobs.data if j.get('recruiter_id')])
        jobs_without_recruiter = total_jobs - jobs_with_recruiter
        
        print(f"📊 Total jobs: {total_jobs}")
        print(f"✅ Jobs avec recruiter_id: {jobs_with_recruiter}")
        print(f"❌ Jobs sans recruiter_id: {jobs_without_recruiter}")
        
    except Exception as e:
        print(f"Erreur lors de la vérification: {e}")
        return
    
    print("\n🔧 EXÉCUTION DE LA MIGRATION")
    print("=" * 50)
    
    # 2. Récupérer les jobs sans recruiter_id avec leurs companies
    try:
        # Requête pour obtenir les jobs sans recruiter_id et les companies correspondantes
        jobs_to_update = []
        
        for job in jobs.data:
            if not job.get('recruiter_id') and job.get('company_id'):
                # Récupérer la company
                company = supabase.table("companies").select("recruiter_id").eq("id", job['company_id']).execute()
                if company.data and company.data[0].get('recruiter_id'):
                    jobs_to_update.append({
                        'job_id': job['id'],
                        'recruiter_id': company.data[0]['recruiter_id'],
                        'title': job['title']
                    })
        
        print(f"🎯 Jobs à mettre à jour: {len(jobs_to_update)}")
        
        # 3. Mettre à jour chaque job
        updated_count = 0
        for job_update in jobs_to_update:
            try:
                result = supabase.table("jobs").update({
                    "recruiter_id": job_update['recruiter_id']
                }).eq("id", job_update['job_id']).execute()
                
                if result.data:
                    print(f"✅ Mis à jour: {job_update['title']}")
                    updated_count += 1
                else:
                    print(f"❌ Échec: {job_update['title']}")
                    
            except Exception as e:
                print(f"❌ Erreur pour {job_update['title']}: {e}")
        
        print(f"\n🎉 {updated_count} jobs mis à jour avec succès!")
        
    except Exception as e:
        print(f"Erreur lors de la migration: {e}")
        return
    
    print("\n🔍 APRÈS MIGRATION")
    print("=" * 50)
    
    # 4. Vérification finale
    try:
        jobs_after = supabase.table("jobs").select("id, title, recruiter_id").execute()
        total_jobs_after = len(jobs_after.data)
        jobs_with_recruiter_after = len([j for j in jobs_after.data if j.get('recruiter_id')])
        jobs_without_recruiter_after = total_jobs_after - jobs_with_recruiter_after
        
        print(f"📊 Total jobs: {total_jobs_after}")
        print(f"✅ Jobs avec recruiter_id: {jobs_with_recruiter_after}")
        print(f"❌ Jobs sans recruiter_id: {jobs_without_recruiter_after}")
        
        # Afficher les jobs qui n'ont toujours pas de recruiter_id
        if jobs_without_recruiter_after > 0:
            print(f"\n⚠️  Jobs sans recruiter_id restants:")
            for job in jobs_after.data:
                if not job.get('recruiter_id'):
                    print(f"   - {job['title']} (ID: {job['id']})")
        
        print(f"\n🎯 RÉSULTAT: {updated_count} jobs mis à jour!")
        
    except Exception as e:
        print(f"Erreur lors de la vérification finale: {e}")

if __name__ == "__main__":
    test_migration()
