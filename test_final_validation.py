#!/usr/bin/env python3
"""
Test final de validation complète du chatbot amélioré
"""

import requests
import json
import base64

def test_feature(user_id, role, query, page_context, description, expected_type):
    """Test une fonctionnalité spécifique"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": user_id, "role": role}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"🧪 {description}")
    print(f"👤 {role} | 💬 {query[:50]}{'...' if len(query) > 50 else ''}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=45)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result['response']
            
            # Analyser le type de réponse
            response_type = "unknown"
            if "**1." in response_text and "Compatibilité:" in response_text:
                response_type = "recommendation"
            elif "**" in response_text and len(response_text) > 500:
                response_type = "specialized_advice"
            elif any(keyword in response_text for keyword in ["conseil", "guide", "étapes"]):
                response_type = "advice"
            elif "Bonjour" in response_text and len(response_text) > 200:
                response_type = "llm_response"
            else:
                response_type = "fallback"
            
            # Vérifier si c'est le type attendu
            success = response_type == expected_type or expected_type == "any"
            status_icon = "✅" if success else "⚠️"
            
            print(f"{status_icon} Status: 200 | Type: {response_type} | Longueur: {len(response_text)}")
            
            # Compter les éléments structurés
            if "**1." in response_text:
                count = response_text.count("**") // 2
                print(f"📊 Éléments: {count}")
            
            # Afficher un extrait
            if len(response_text) > 100:
                print(f"📝 {response_text[:100]}...")
            else:
                print(f"📝 {response_text}")
                
            return success
            
        else:
            print(f"❌ Status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False
    
    finally:
        print("-" * 60)

def main():
    print("🎯 TEST FINAL DE VALIDATION COMPLÈTE")
    print("=" * 60)
    
    # IDs des utilisateurs
    recruiter_id = "e714bb4b-b3f8-4343-8d5d-1e120e421b29"  # John Doe
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"  # Mohamed ouabbi
    
    tests_passed = 0
    total_tests = 0
    
    print("🔥 SECTION 1: RECOMMANDATIONS IA")
    print("=" * 60)
    
    # Test 1: Recommandations candidat
    total_tests += 1
    if test_feature(
        candidate_id, "candidate",
        "Recommande-moi des offres d'emploi", 
        "recherche_emploi",
        "Recommandations candidat",
        "recommendation"
    ):
        tests_passed += 1
    
    # Test 2: Recommandations recruteur
    total_tests += 1
    if test_feature(
        recruiter_id, "recruiter",
        "Candidats pour l'offre 11111111-aaaa-bbbb-cccc-111111111111", 
        "recherche_emploi",
        "Recommandations recruteur",
        "recommendation"
    ):
        tests_passed += 1
    
    print("🔥 SECTION 2: CONSEILS SPÉCIALISÉS RECRUTEURS")
    print("=" * 60)
    
    # Test 3: Évaluation compétences
    total_tests += 1
    if test_feature(
        recruiter_id, "recruiter",
        "Comment évaluer les compétences techniques ?", 
        "conseil",
        "Évaluation compétences",
        "specialized_advice"
    ):
        tests_passed += 1
    
    # Test 4: Questions entretien
    total_tests += 1
    if test_feature(
        recruiter_id, "recruiter",
        "Questions pour entretien développeur", 
        "entretien",
        "Questions entretien",
        "specialized_advice"
    ):
        tests_passed += 1
    
    print("🔥 SECTION 3: CONSEILS SPÉCIALISÉS CANDIDATS")
    print("=" * 60)
    
    # Test 5: Amélioration CV
    total_tests += 1
    if test_feature(
        candidate_id, "candidate",
        "Comment améliorer mon CV ?", 
        "conseil",
        "Amélioration CV",
        "specialized_advice"
    ):
        tests_passed += 1
    
    # Test 6: Négociation salaire
    total_tests += 1
    if test_feature(
        candidate_id, "candidate",
        "Comment négocier mon salaire ?", 
        "conseil",
        "Négociation salaire",
        "specialized_advice"
    ):
        tests_passed += 1
    
    print("🔥 SECTION 4: RECHERCHE & LLM")
    print("=" * 60)
    
    # Test 7: Recherche normale
    total_tests += 1
    if test_feature(
        candidate_id, "candidate",
        "Data Scientist", 
        "recherche_emploi",
        "Recherche Data Scientist",
        "any"
    ):
        tests_passed += 1
    
    # Test 8: Préparation entretien
    total_tests += 1
    if test_feature(
        candidate_id, "candidate",
        "Préparer entretien technique", 
        "entretien",
        "Préparation entretien",
        "any"
    ):
        tests_passed += 1
    
    print("🔥 SECTION 5: INFORMATIONS CONTRACTUELLES")
    print("=" * 60)
    
    # Test 9: CDI vs CDD
    total_tests += 1
    if test_feature(
        candidate_id, "candidate",
        "Différence CDI CDD", 
        "contrats",
        "CDI vs CDD",
        "any"
    ):
        tests_passed += 1
    
    # Test 10: Choix contrat recruteur
    total_tests += 1
    if test_feature(
        recruiter_id, "recruiter",
        "Quand proposer CDD vs CDI ?", 
        "contrats",
        "Choix contrat",
        "specialized_advice"
    ):
        tests_passed += 1
    
    print("🔥 SECTION 6: GESTION MULTI-UTILISATEURS")
    print("=" * 60)
    
    # Test 11: Candidat différent
    total_tests += 1
    if test_feature(
        "df82c6a7-72cd-4e06-ad09-5a4d0127bca4", "candidate",
        "Recommandations emploi", 
        "recherche_emploi",
        "Autre candidat",
        "any"
    ):
        tests_passed += 1
    
    # Test 12: Recherche Frontend
    total_tests += 1
    if test_feature(
        candidate_id, "candidate",
        "Frontend React TypeScript", 
        "recherche_emploi",
        "Recherche Frontend",
        "any"
    ):
        tests_passed += 1
    
    # Résultats finaux
    print("🎯 RÉSULTATS FINAUX")
    print("=" * 60)
    
    success_rate = (tests_passed / total_tests) * 100
    
    print(f"✅ Tests réussis: {tests_passed}/{total_tests}")
    print(f"📊 Taux de réussite: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🌟 EXCELLENT! Système production-ready")
    elif success_rate >= 80:
        print("✅ TRÈS BON! Quelques améliorations mineures")
    elif success_rate >= 70:
        print("⚠️ BON! Améliorations nécessaires")
    else:
        print("❌ PROBLÈMES! Révision majeure requise")
    
    print("\n🚀 FONCTIONNALITÉS VALIDÉES:")
    print("- ✅ Recommandations IA avec scores")
    print("- ✅ Conseils spécialisés par rôle")
    print("- ✅ Recherche intelligente")
    print("- ✅ Gestion multi-utilisateurs")
    print("- ✅ Informations contractuelles")
    print("- ✅ Fallbacks robustes")

if __name__ == "__main__":
    main()
