#!/usr/bin/env python3
"""
Script pour récupérer des IDs de test depuis Supabase
"""

import os
from dotenv import load_dotenv
from supabase import create_client

load_dotenv()

def get_test_ids():
    # Initialiser Supabase
    supabase = create_client(
        os.getenv("SUPABASE_URL"),
        os.getenv("SUPABASE_KEY")
    )
    
    print("=== Récupération des IDs de test ===\n")
    
    # Récupérer quelques candidats
    print("📋 Candidats disponibles :")
    try:
        candidates = supabase.table("candidates").select("id, email, full_name").limit(3).execute()
        if candidates.data:
            for candidate in candidates.data:
                print(f"  - ID: {candidate['id']}")
                print(f"    Email: {candidate.get('email', 'N/A')}")
                print(f"    Nom: {candidate.get('full_name', 'N/A')}")
                print()
        else:
            print("  Aucun candidat trouvé")
    except Exception as e:
        print(f"  Erreur: {e}")
    
    print("-" * 50)
    
    # R<PERSON>cupérer quelques recruteurs
    print("🏢 Recruteurs disponibles :")
    try:
        recruiters = supabase.table("recruiters").select("id, email, full_name").limit(3).execute()
        if recruiters.data:
            for recruiter in recruiters.data:
                print(f"  - ID: {recruiter['id']}")
                print(f"    Email: {recruiter.get('email', 'N/A')}")
                print(f"    Nom: {recruiter.get('full_name', 'N/A')}")
                print()
        else:
            print("  Aucun recruteur trouvé")
    except Exception as e:
        print(f"  Erreur: {e}")
    
    print("-" * 50)
    
    # Récupérer quelques jobs
    print("💼 Jobs disponibles :")
    try:
        jobs = supabase.table("jobs").select("id, title, location").limit(3).execute()
        if jobs.data:
            for job in jobs.data:
                print(f"  - ID: {job['id']}")
                print(f"    Titre: {job.get('title', 'N/A')}")
                print(f"    Lieu: {job.get('location', 'N/A')}")
                print()
        else:
            print("  Aucun job trouvé")
    except Exception as e:
        print(f"  Erreur: {e}")

if __name__ == "__main__":
    get_test_ids()
