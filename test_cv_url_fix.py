#!/usr/bin/env python3
"""
Test pour vérifier que les URLs CV sont maintenant en HTTP au lieu de file://
"""
import requests
import json
import time

def test_cv_api():
    """Test l'API CV pour vérifier les URLs HTTP"""
    print("🧪 Test des URLs CV - HTTP vs file://")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    # Test 1: Vérifier l'endpoint /cv
    print("📡 Test 1: GET /cv")
    try:
        response = requests.get(f"{base_url}/cv", timeout=5)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2)}")
            
            # Vérifier si cv_url est présent et est une URL HTTP
            if 'cv_url' in data:
                cv_url = data['cv_url']
                if cv_url.startswith('http://') or cv_url.startswith('https://'):
                    print("   ✅ cv_url est une URL HTTP valide!")
                    
                    # Test 2: Vérifier que l'URL fonctionne
                    print(f"\n📡 Test 2: Accès direct à {cv_url}")
                    try:
                        cv_response = requests.get(cv_url, timeout=5)
                        print(f"   Status: {cv_response.status_code}")
                        if cv_response.status_code == 200:
                            print(f"   Content-Type: {cv_response.headers.get('Content-Type')}")
                            print(f"   Content-Length: {len(cv_response.content)} bytes")
                            print("   ✅ Fichier CV accessible via HTTP!")
                        else:
                            print(f"   ❌ Erreur d'accès au fichier: {cv_response.status_code}")
                    except Exception as e:
                        print(f"   ❌ Erreur d'accès: {e}")
                        
                elif cv_url.startswith('file://'):
                    print("   ❌ cv_url est encore une URL file:// (problème non résolu)")
                else:
                    print(f"   ⚠️  cv_url format inattendu: {cv_url}")
            else:
                print("   ⚠️  cv_url non présent dans la réponse")
                
        elif response.status_code == 401:
            print("   ⚠️  Authentification requise - testez avec un token valide")
        else:
            print(f"   ❌ Erreur API: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Impossible de se connecter au serveur")
        print("   💡 Assurez-vous que le serveur Flask est démarré")
        return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False
    
    # Test 3: Vérifier la route statique directement
    print(f"\n📡 Test 3: Route statique /uploads/cvs/")
    test_filename = "1879125b-271e-4e2a-b7fb-0058ccce4e59_cv.pdf"  # Fichier connu
    static_url = f"{base_url}/uploads/cvs/{test_filename}"
    
    try:
        static_response = requests.get(static_url, timeout=5)
        print(f"   URL: {static_url}")
        print(f"   Status: {static_response.status_code}")
        
        if static_response.status_code == 200:
            print(f"   Content-Type: {static_response.headers.get('Content-Type')}")
            print(f"   Content-Length: {len(static_response.content)} bytes")
            print("   ✅ Route statique fonctionne!")
        else:
            print(f"   ❌ Fichier non accessible: {static_response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Erreur route statique: {e}")
    
    return True

def test_cv_download_route():
    """Test la route /cv/download/<filename>"""
    print(f"\n📡 Test 4: Route /cv/download/")
    
    base_url = "http://localhost:5000"
    test_filename = "1879125b-271e-4e2a-b7fb-0058ccce4e59_cv.pdf"
    download_url = f"{base_url}/cv/download/{test_filename}"
    
    try:
        response = requests.get(download_url, timeout=5)
        print(f"   URL: {download_url}")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   Content-Type: {response.headers.get('Content-Type')}")
            print(f"   Content-Length: {len(response.content)} bytes")
            print("   ✅ Route download fonctionne!")
        else:
            print(f"   ❌ Download failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Erreur download: {e}")

if __name__ == "__main__":
    print("🚀 Test de la correction des URLs CV")
    print("Objectif: Vérifier que les URLs sont en HTTP et non file://")
    print()
    
    success = test_cv_api()
    if success:
        test_cv_download_route()
    
    print("\n" + "=" * 60)
    print("🎯 RÉSULTAT ATTENDU:")
    print("✅ cv_url doit commencer par http://localhost:5000/uploads/cvs/")
    print("✅ Le fichier doit être accessible via cette URL")
    print("❌ Plus d'URLs file:// dans les réponses API")
    print("=" * 60)
