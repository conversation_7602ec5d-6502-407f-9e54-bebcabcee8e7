#!/usr/bin/env python3
"""
Test direct et rapide de votre commande curl
"""

import requests
import json
import sys

def test_direct():
    """Test direct sans chargement de modules lourds"""
    
    url = "http://localhost:5000/chat/"
    
    # Votre requete exacte
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "Origin": "http://localhost:3000"
    }
    
    body = {
        "query": "donner moi les poste   *",
        "pageContext": "",
        "role": "candidate",
        "user": {
            "id": "63800628-55a2-4c33-97ce-5bcc5ee6a1bb",
            "role": "candidate"
        }
    }
    
    print("🧪 Test direct de votre curl")
    print("=" * 40)
    print(f"URL: {url}")
    print(f"Query: '{body['query']}'")
    print(f"User: Taoufiq ({body['user']['id'][:8]}...)")
    print("=" * 40)
    
    try:
        print("📡 Test de connexion...")
        
        # Test rapide de connexion
        test_response = requests.get("http://localhost:5000/", timeout=3)
        print(f"✅ Serveur accessible (Status: {test_response.status_code})")
        
        print("📡 Envoi de votre requete curl...")
        
        # Votre requete exacte
        response = requests.post(url, headers=headers, json=body, timeout=30)
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("🎉 SUCCESS!")
            
            json_response = response.json()
            response_text = json_response.get('response', '')
            
            print(f"\n📝 Reponse ({len(response_text)} caracteres):")
            print("-" * 50)
            print(response_text)
            print("-" * 50)
            
            # Verifications rapides
            checks = [
                ("Nom Taoufiq present", "Taoufiq" in response_text),
                ("Reponse en francais", any(w in response_text.lower() for w in ["bonjour", "voici", "offre"])),
                ("Contient des offres", any(w in response_text.lower() for w in ["poste", "emploi", "offre"])),
                ("Reponse substantielle", len(response_text) > 50),
                ("Format structure", "**" in response_text or "*" in response_text)
            ]
            
            print("\n🔍 Verifications:")
            score = 0
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"  {status} {check_name}")
                if result:
                    score += 1
            
            percentage = (score / len(checks)) * 100
            print(f"\n🎯 Score: {score}/{len(checks)} ({percentage:.0f}%)")
            
            if percentage >= 80:
                print("🎉 EXCELLENT! Votre integration LLM fonctionne parfaitement!")
            elif percentage >= 60:
                print("👍 BIEN! Integration fonctionnelle")
            else:
                print("⚠️ Ameliorations possibles")
            
            return True
            
        else:
            print(f"❌ ERREUR - Status: {response.status_code}")
            try:
                error = response.json()
                print(f"Erreur: {error}")
            except:
                print(f"Erreur brute: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ SERVEUR NON ACCESSIBLE")
        print("💡 Demarrez le serveur avec: python run.py")
        print("⏳ Attendez 2-3 minutes que les modeles AI se chargent")
        return False
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Test direct de votre commande curl exacte")
    success = test_direct()
    
    if success:
        print("\n✅ Test REUSSI!")
        print("🎯 Votre curl fonctionne parfaitement")
    else:
        print("\n❌ Test ECHOUE")
        print("🔧 Verifiez que le serveur Flask est demarre")
    
    sys.exit(0 if success else 1)
