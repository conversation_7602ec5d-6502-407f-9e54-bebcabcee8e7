#!/bin/bash

# Ton script original avec la correction base64

URL="http://127.0.0.1:5000/chat/"
HEADER="Content-Type: application/json"

# Utilise un ID valide de ta base (remplace si nécessaire)
USER_JSON='{"id":"4db01413-00e5-4c1c-90f7-4c03438d1dc3","role":"candidate"}'

# 🔧 SEULE MODIFICATION: Ajouter | tr -d '\n' à la fin
USER_B64=$(echo -n "$USER_JSON" | base64 | tr -d '\n')

declare -a QUERIES=(
  '{"query": "Comment améliorer mon CV pour un poste de data scientist ?", "pageContext": "conseil"}'
  '{"query": "Que dois-je mettre dans une lettre de motivation pour une startup tech ?", "pageContext": "lettre_motivation"}'
  '{"query": "Quelle est la différence entre un CDI et un CDD ?", "pageContext": "contrats"}'
  '{"query": "Comment me préparer à un entretien développeur backend ?", "pageContext": "entretien"}'
  '{"query": "Est-ce qu'il faut apprendre Docker pour trouver un job de développeur ?", "pageContext": "skills"}'
)

for query in "${QUERIES[@]}"
do
  echo ">>> Question: $query"
  curl -s -X POST "$URL" \
    -H "$HEADER" \
    -H "X-User: $USER_B64" \
    -d "$query"
  echo -e "\n----------------------------"
done
