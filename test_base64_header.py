#!/usr/bin/env python3
"""
Test avec header X-User encodé en base64
"""

import requests
import json
import base64

# IDs réels de ta base
REAL_CANDIDATE_ID = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"
REAL_RECRUITER_ID = "6d2df96a-b6b2-4098-b5f3-1192fba544f7"

def encode_user_header(user_id, role):
    """Encode les données utilisateur en base64 pour le header"""
    user_data = {"id": user_id, "role": role}
    user_json = json.dumps(user_data)
    encoded = base64.b64encode(user_json.encode('utf-8')).decode('utf-8')
    return encoded

def test_chat_base64(user_id, role, query, test_name):
    """Test l'endpoint chat avec header base64"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header en base64
    x_user_encoded = encode_user_header(user_id, role)
    
    headers = {
        "Content-Type": "application/json",
        "X-User": x_user_encoded
    }
    
    data = {
        "query": query,
        "pageContext": "homepage"
    }
    
    print(f"=== {test_name} ===")
    print(f"🔄 User ID: {user_id[:8]}... (role: {role})")
    print(f"🔄 Query: {query}")
    print(f"🔄 Header X-User (base64): {x_user_encoded[:30]}...")
    
    try:
        response = requests.post(url, headers=headers, json=data)
        
        print(f"\n📊 Résultat:")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Succès!")
            print(f"   💬 Réponse: {result['response'][:100]}...")
        else:
            error = response.json()
            print(f"   ❌ Erreur: {error}")
            
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    print("\n" + "="*60 + "\n")

def main():
    print("🤖 Test du chatbot avec header X-User base64")
    print("="*60)
    
    # Test 1: Candidat réel
    test_chat_base64(
        REAL_CANDIDATE_ID, 
        "candidate", 
        "Développeur Python", 
        "Test 1: Candidat réel"
    )
    
    # Test 2: Recruteur réel
    test_chat_base64(
        REAL_RECRUITER_ID, 
        "recruiter", 
        "Frontend Developer", 
        "Test 2: Recruteur réel"
    )
    
    # Test 3: Candidat inexistant
    test_chat_base64(
        "00000000-0000-0000-0000-000000000000", 
        "candidate", 
        "Test", 
        "Test 3: Candidat inexistant (doit échouer)"
    )
    
    # Test 4: Header invalide (non base64)
    print("=== Test 4: Header non-base64 (doit échouer) ===")
    url = "http://127.0.0.1:5000/chat/"
    headers = {
        "Content-Type": "application/json",
        "X-User": "invalid_header_not_base64"
    }
    data = {"query": "Test", "pageContext": "homepage"}
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"Status: {response.status_code}")
        if response.status_code == 400:
            print(f"✅ Erreur attendue: {response.json()}")
        else:
            print(f"❌ Réponse inattendue: {response.json()}")
    except Exception as e:
        print(f"💥 Exception: {e}")
    
    print("\n" + "="*60 + "\n")
    
    # Test 5: Sans header
    print("=== Test 5: Sans header X-User (doit échouer) ===")
    headers_no_user = {"Content-Type": "application/json"}
    
    try:
        response = requests.post(url, headers=headers_no_user, json=data)
        print(f"Status: {response.status_code}")
        if response.status_code == 401:
            print(f"✅ Erreur attendue: {response.json()}")
        else:
            print(f"❌ Réponse inattendue: {response.json()}")
    except Exception as e:
        print(f"💥 Exception: {e}")

if __name__ == "__main__":
    main()
