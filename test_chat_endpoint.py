#!/usr/bin/env python3
"""
Script de test pour l'endpoint /chat/ avec header X-User et vérification utilisateur
"""

import requests
import json

def test_chat_endpoint():
    url = "http://127.0.0.1:5000/chat/"

    data = {
        "query": "Développeur",
        "pageContext": "homepage"
    }

    # Test 1: Avec un candidat existant (remplace par un ID réel de ta base)
    headers_candidate = {
        "Content-Type": "application/json",
        "X-User": json.dumps({"id": "candidate_real_id", "role": "candidate"})
    }

    print("=== Test 1: Candidat existant ===")
    try:
        response = requests.post(url, headers=headers_candidate, json=data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Erreur: {e}")

    print("\n" + "="*50 + "\n")

    # Test 2: Avec un recruteur existant (remplace par un ID réel de ta base)
    headers_recruiter = {
        "Content-Type": "application/json",
        "X-User": json.dumps({"id": "recruiter_real_id", "role": "recruiter"})
    }

    print("=== Test 2: Recruteur existant ===")
    try:
        response = requests.post(url, headers=headers_recruiter, json=data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Erreur: {e}")

    print("\n" + "="*50 + "\n")

    # Test 3: Candidat inexistant (doit échouer avec 404)
    headers_fake_candidate = {
        "Content-Type": "application/json",
        "X-User": json.dumps({"id": "fake_candidate_123", "role": "candidate"})
    }

    print("=== Test 3: Candidat inexistant (doit échouer) ===")
    try:
        response = requests.post(url, headers=headers_fake_candidate, json=data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Erreur: {e}")

    print("\n" + "="*50 + "\n")

    # Test 4: Sans header X-User (doit échouer avec 401)
    headers_no_user = {
        "Content-Type": "application/json"
    }

    print("=== Test 4: Sans header X-User (doit échouer) ===")
    try:
        response = requests.post(url, headers=headers_no_user, json=data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Erreur: {e}")

    print("\n" + "="*50 + "\n")

    # Test 5: Rôle invalide (doit échouer avec 403)
    headers_invalid_role = {
        "Content-Type": "application/json",
        "X-User": json.dumps({"id": "user123", "role": "admin"})
    }

    print("=== Test 5: Rôle invalide (doit échouer) ===")
    try:
        response = requests.post(url, headers=headers_invalid_role, json=data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Erreur: {e}")

if __name__ == "__main__":
    test_chat_endpoint()
