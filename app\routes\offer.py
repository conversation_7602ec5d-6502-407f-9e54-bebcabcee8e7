from flask import Blueprint, jsonify, current_app, request

offer_bp = Blueprint("offer", __name__)

# 🔹 Créer une nouvelle offre (job)
@offer_bp.route("/create", methods=["POST"])
def create_offer():
    try:
        supabase = current_app.supabase
        data = request.json

        # Validation des données
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Récupérer l'ID du recruteur depuis les headers
        recruiter_id = request.headers.get("X-Recruiter-ID")
        if not recruiter_id:
            return jsonify({"error": "Recruiter ID is required"}), 401

        print(f"Creating job with data: {data}")  # Debug log
        print(f"Recruiter ID: {recruiter_id}")  # Debug log

        # Validation des champs requis
        required_fields = ["title", "description", "company_id"]
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"Field '{field}' is required"}), 400

        # P<PERSON>parer les données pour l'insertion
        job_data = {
            "company_id": data.get("company_id"),
            "title": data.get("title"),
            "description": data.get("description"),
            "location": data.get("location"),
            "requirements": data.get("requirements"),
            "education": data.get("education"),
            "file_url": data.get("file_url"),
            "contract_type": data.get("contract_type"),
            "work_mode": data.get("work_mode"),
            "salary_min": data.get("salary_min"),
            "salary_max": data.get("salary_max"),
            "salary_currency": data.get("salary_currency", "EUR"),
            "skills": data.get("skills"),
            "is_active": data.get("is_active", True),
            "match_criteria": data.get("match_criteria"),
            "experience": data.get("experience"),
            "deadline": data.get("deadline"),
            "salary": data.get("salary"),
            "recruiter_id": recruiter_id
        }

        # Supprimer les valeurs None
        job_data = {k: v for k, v in job_data.items() if v is not None}

        print(f"Cleaned job_data: {job_data}")  # Debug log

        # Vérifier que la company existe et appartient au recruteur
        try:
            company_check = supabase.table("companies").select("id, recruiter_id").eq("id", job_data["company_id"]).execute()
            if not company_check.data:
                return jsonify({"error": "Company not found"}), 404

            company = company_check.data[0]
            if company["recruiter_id"] != recruiter_id:
                return jsonify({"error": "You can only create jobs for your own company"}), 403

        except Exception as company_error:
            print(f"Error checking company: {company_error}")
            return jsonify({"error": f"Error validating company: {str(company_error)}"}), 500

        # Créer le job
        try:
            response = supabase.table("jobs").insert(job_data).execute()
            print(f"Supabase response: {response}")  # Debug log

            if response.data:
                return jsonify({
                    "message": "Job offer created successfully",
                    "data": response.data[0]
                }), 201
            else:
                return jsonify({"error": "Failed to create job offer"}), 500

        except Exception as insert_error:
            print(f"Error during job creation: {insert_error}")
            return jsonify({"error": f"Job creation failed: {str(insert_error)}"}), 500

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error creating job: {str(e)}")  # Debug log
        print(f"Full traceback: {error_details}")  # Debug log
        return jsonify({
            "error": str(e),
            "details": error_details,
            "received_data": data if 'data' in locals() else None
        }), 500

# 🔹 Récupérer toutes les offres (jobs)
@offer_bp.route("/", methods=["GET"])
def get_offers():
    try:
        supabase = current_app.supabase
        result = supabase.table("jobs").select("*").execute()
        return jsonify(result.data), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500
# 🔹 Récupérer une offre par ID
@offer_bp.route('/<job_id>', methods=['GET'])
def get_offer(job_id):
    try:
        supabase = current_app.supabase
        result = supabase.table("jobs").select("*").eq("id", job_id).single().execute()
        if result.data:
            return jsonify(result.data), 200
        return jsonify({"error": "Job offer not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 🔹 Mettre à jour une offre par ID
@offer_bp.route('/<job_id>', methods=['PUT'])
def update_offer(job_id):
    try:
        supabase = current_app.supabase
        data = request.json

        # Validation des données
        if not data:
            return jsonify({"error": "No data provided"}), 400

        print(f"Updating job {job_id} with data: {data}")  # Debug log

        update_data = {
            "company_id": data.get("company_id"),
            "title": data.get("title"),
            "description": data.get("description"),
            "location": data.get("location"),
            "requirements": data.get("requirements"),
            "education": data.get("education"),
            "file_url": data.get("file_url"),
            "contract_type": data.get("contract_type"),
            "work_mode": data.get("work_mode"),
            "salary_min": data.get("salary_min"),
            "salary_max": data.get("salary_max"),
            "salary_currency": data.get("salary_currency"),
            "skills": data.get("skills"),
            "is_active": data.get("is_active"),
            "match_criteria": data.get("match_criteria"),
            "experience": data.get("experience"),
            "deadline": data.get("deadline"),
            "salary": data.get("salary"),
            "recruiter_id": data.get("recruiter_id")
        }

        # Remove None values
        update_data = {k: v for k, v in update_data.items() if v is not None}

        print(f"Cleaned update_data: {update_data}")  # Debug log

        # Vérifier que le job existe
        try:
            existing_job = supabase.table("jobs").select("id").eq("id", job_id).execute()
            print(f"Existing job check: {existing_job}")  # Debug log
            if not existing_job.data:
                return jsonify({"error": "Job not found"}), 404
        except Exception as check_error:
            print(f"Error checking job existence: {check_error}")
            return jsonify({"error": f"Error checking job: {str(check_error)}"}), 500

        # Effectuer la mise à jour
        try:
            response = supabase.table("jobs").update(update_data).eq("id", job_id).execute()
            print(f"Supabase response: {response}")  # Debug log
        except Exception as update_error:
            print(f"Error during update: {update_error}")
            return jsonify({"error": f"Update failed: {str(update_error)}"}), 500

        return jsonify({"message": "Job offer updated successfully", "data": response.data}), 200
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error updating job {job_id}: {str(e)}")  # Debug log
        print(f"Full traceback: {error_details}")  # Debug log
        return jsonify({
            "error": str(e),
            "details": error_details,
            "job_id": job_id,
            "received_data": data
        }), 500

# 🔹 Récupérer les offres par company_id
@offer_bp.route('/company/<company_id>', methods=['GET'])
def get_offers_by_company(company_id):
    try:
        supabase = current_app.supabase
        result = supabase.table("jobs").select("*").eq("company_id", company_id).execute()
        return jsonify(result.data), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 🔹 Supprimer une offre par ID
@offer_bp.route('/<job_id>', methods=['DELETE'])
def delete_offer(job_id):
    try:
        supabase = current_app.supabase

        print(f"Attempting to delete job: {job_id}")  # Debug log

        # Vérifier que le job existe
        existing_job = supabase.table("jobs").select("id").eq("id", job_id).execute()
        if not existing_job.data:
            return jsonify({"error": "Job not found"}), 404

        # Supprimer les enregistrements liés en cascade
        try:
            # 1. Supprimer les candidatures (applications)
            applications_result = supabase.table("applications").delete().eq("job_id", job_id).execute()
            print(f"Deleted applications: {len(applications_result.data) if applications_result.data else 0}")

            # 2. Supprimer les correspondances candidat-job (candidate_job_matches)
            matches_result = supabase.table("candidate_job_matches").delete().eq("job_id", job_id).execute()
            print(f"Deleted candidate job matches: {len(matches_result.data) if matches_result.data else 0}")

            # 3. Supprimer les entretiens (interviews)
            interviews_result = supabase.table("interviews").delete().eq("job_id", job_id).execute()
            print(f"Deleted interviews: {len(interviews_result.data) if interviews_result.data else 0}")

        except Exception as cascade_error:
            print(f"Error during cascade deletion: {cascade_error}")
            # Continue with job deletion even if some related records fail to delete
            # This handles cases where related tables might not exist or be empty

        # 4. Supprimer le job principal
        response = supabase.table("jobs").delete().eq("id", job_id).execute()
        print(f"Delete job response: {response}")  # Debug log

        return jsonify({"message": "Job offer and related data deleted successfully"}), 200
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error deleting job {job_id}: {str(e)}")
        print(f"Full traceback: {error_details}")
        return jsonify({
            "error": str(e),
            "details": error_details,
            "job_id": job_id
        }), 500

