from flask import current_app
from typing import Dict, List, Set, Tuple
import json
from app.services.matching_service import get_matching_service

def calculate_candidate_job_scores(candidate_id: str, job_ids: List[str] = None, limit: int = 20) -> List[Dict]:
    """
    Calcule les scores réels entre un candidat et des offres d'emploi en utilisant le service de matching

    Args:
        candidate_id (str): UUID du candidat
        job_ids (List[str], optional): Liste spécifique d'IDs de jobs à analyser
        limit (int): Nombre maximum de jobs à analyser

    Returns:
        List[Dict]: Liste des jobs avec leurs scores de matching réels
    """
    try:
        print(f"[INFO] Calcul des scores réels pour candidat {candidate_id}")

        # Utiliser le service de matching existant
        matching_service = get_matching_service()
        matches = matching_service.match_candidate_to_jobs(candidate_id, job_ids, limit)

        if not matches:
            print("[WARNING] Aucun match trouvé avec le service de matching")
            return []

        # Transformer les résultats pour notre analyse
        scored_jobs = []
        for match in matches:
            scored_job = {
                'job_id': match.get('job_id'),
                'job_title': match.get('job_title', ''),
                'job_location': match.get('job_location', ''),
                'job_skills': match.get('job_skills', []),
                'candidate_skills': match.get('candidate_skills', []),
                'matched_skills': match.get('matched_skills', []),
                'match_score': match.get('match_score', 0),
                'match_percentage': match.get('match_percentage', 0),
                'sbert_similarity': match.get('sbert_similarity', 0),
                'skill2vec_similarity': match.get('skill2vec_similarity', 0),
                'prediction': match.get('prediction', 'no_match')
            }
            scored_jobs.append(scored_job)

        print(f"[INFO] Calculé {len(scored_jobs)} scores de matching")
        return scored_jobs

    except Exception as e:
        print(f"[ERROR] Erreur dans calculate_candidate_job_scores: {e}")
        return []

def recommendation_candidate_rh(candidate_id: str, display_name: str) -> str:
    """
    Analyse le profil d'un candidat et les offres d'emploi pour donner des recommandations 
    d'amélioration du score de matching entre job et profile_candidate
    
    Args:
        candidate_id (str): UUID du candidat
        display_name (str): Nom d'affichage du candidat
        
    Returns:
        str: Recommandations personnalisées pour améliorer le matching
    """
    try:
        print(f"[INFO] Génération de recommandations RH pour candidat {candidate_id}")
        
        supabase = current_app.supabase
        
        # 1. Récupérer le profil complet du candidat
        candidate_profile = supabase.table("candidate_profiles") \
            .select("skillner_skills, py_skills, added_skills, skills, experience, education, cv, location") \
            .eq("candidate_id", candidate_id) \
            .execute()
        
        if not candidate_profile.data:
            return f"Bonjour {display_name} ! Je n'ai pas pu accéder à votre profil. Veuillez vous assurer que votre profil est complet."
        
        profile = candidate_profile.data[0]
        
        # 2. Combiner toutes les compétences du candidat
        candidate_skills = set()
        
        # Récupérer les compétences de toutes les sources
        skill_sources = ['skillner_skills', 'py_skills', 'added_skills', 'skills']
        for source in skill_sources:
            skills_list = profile.get(source)
            if skills_list and isinstance(skills_list, list):
                candidate_skills.update([skill.strip().lower() for skill in skills_list if skill and skill.strip()])
        
        candidate_skills = list(candidate_skills)
        
        # 3. Calculer les scores réels avec le service de matching
        scored_jobs = calculate_candidate_job_scores(candidate_id, limit=20)

        if not scored_jobs:
            return f"Bonjour {display_name} ! Aucune offre d'emploi n'est actuellement disponible pour l'analyse ou votre profil n'est pas assez complet."

        # 4. Analyser les compétences et scores
        all_job_skills = set()
        job_skills_frequency = {}
        location_frequency = {}
        job_scores = {}  # Nouveau: stocker les scores par job

        # Récupérer les détails des jobs pour l'analyse géographique
        job_ids = [job['job_id'] for job in scored_jobs if job.get('job_id')]
        jobs_details = supabase.table("jobs") \
            .select("id, location") \
            .in_("id", job_ids) \
            .execute()

        job_locations = {job['id']: job.get('location', '') for job in jobs_details.data}

        for scored_job in scored_jobs:
            job_id = scored_job.get('job_id')

            # Analyser les compétences du job
            job_skills = scored_job.get('job_skills', []) or []

            for skill in job_skills:
                if skill and isinstance(skill, str):
                    skill_clean = skill.strip().lower()
                    if skill_clean:
                        all_job_skills.add(skill_clean)
                        job_skills_frequency[skill_clean] = job_skills_frequency.get(skill_clean, 0) + 1

            # Analyser les localisations
            location = job_locations.get(job_id, '').strip()
            if location:
                location_frequency[location] = location_frequency.get(location, 0) + 1

            # Stocker le score du job
            if job_id:
                job_scores[job_id] = {
                    'match_score': scored_job.get('match_score', 0),
                    'match_percentage': scored_job.get('match_percentage', 0),
                    'matched_skills': scored_job.get('matched_skills', []),
                    'title': scored_job.get('job_title', '')
                }
        
        # 5. Identifier les compétences manquantes
        candidate_skills_set = set(candidate_skills)
        missing_skills = all_job_skills - candidate_skills_set
        
        # 6. Trier les compétences manquantes par fréquence (les plus demandées)
        missing_skills_ranked = sorted(
            missing_skills, 
            key=lambda x: job_skills_frequency.get(x, 0), 
            reverse=True
        )[:10]  # Top 10 compétences manquantes
        
        # 7. Identifier les compétences que le candidat possède déjà
        matching_skills = candidate_skills_set & all_job_skills
        matching_skills_ranked = sorted(
            matching_skills,
            key=lambda x: job_skills_frequency.get(x, 0),
            reverse=True
        )[:5]  # Top 5 compétences correspondantes
        
        # 8. Analyser la localisation
        candidate_location = profile.get('location', '').strip()
        location_recommendations = []
        if candidate_location:
            # Vérifier si la localisation du candidat correspond aux offres
            matching_locations = [loc for loc in location_frequency.keys() 
                                if candidate_location.lower() in loc.lower() or loc.lower() in candidate_location.lower()]
            if not matching_locations:
                top_locations = sorted(location_frequency.items(), key=lambda x: x[1], reverse=True)[:3]
                location_recommendations = [loc[0] for loc in top_locations]
        
        # 9. Analyser les performances actuelles
        current_scores = [job_scores[job_id]['match_percentage'] for job_id in job_scores.keys()]
        avg_current_score = sum(current_scores) / len(current_scores) if current_scores else 0
        best_matches = sorted(job_scores.items(), key=lambda x: x[1]['match_percentage'], reverse=True)[:3]

        # 10. Générer les recommandations personnalisées
        response = f"Bonjour {display_name} ! 🎯\n\n"
        response += "**📊 ANALYSE DE VOTRE PROFIL ET RECOMMANDATIONS**\n\n"

        # Score actuel moyen
        response += f"**📈 VOTRE SCORE ACTUEL MOYEN : {avg_current_score:.1f}%**\n\n"

        # Meilleures correspondances actuelles
        if best_matches:
            response += "**🏆 VOS MEILLEURES CORRESPONDANCES ACTUELLES :**\n"
            for i, (job_id, job_data) in enumerate(best_matches, 1):
                response += f"{i}. {job_data['title']} - {job_data['match_percentage']:.1f}%\n"
            response += "\n"

        # Compétences actuelles
        if matching_skills_ranked:
            response += "**✅ VOS POINTS FORTS :**\n"
            for i, skill in enumerate(matching_skills_ranked, 1):
                frequency = job_skills_frequency.get(skill, 0)
                response += f"{i}. {skill.title()} (demandée dans {frequency} offres)\n"
            response += "\n"
        
        # Compétences à développer
        if missing_skills_ranked:
            response += "**🚀 COMPÉTENCES À DÉVELOPPER PRIORITAIREMENT :**\n"
            for i, skill in enumerate(missing_skills_ranked[:5], 1):
                frequency = job_skills_frequency.get(skill, 0)
                response += f"{i}. {skill.title()} (demandée dans {frequency} offres)\n"
            response += "\n"
            
            response += "**💡 CONSEILS D'APPRENTISSAGE :**\n"
            response += "- Commencez par les 2-3 premières compétences listées\n"
            response += "- Suivez des formations en ligne (Coursera, Udemy, OpenClassrooms)\n"
            response += "- Pratiquez sur des projets personnels\n"
            response += "- Obtenez des certifications reconnues\n\n"
        
        # Recommandations de localisation
        if location_recommendations:
            response += "**📍 OPPORTUNITÉS GÉOGRAPHIQUES :**\n"
            response += f"Votre localisation actuelle : {candidate_location}\n"
            response += "Localisations avec le plus d'opportunités :\n"
            for i, location in enumerate(location_recommendations, 1):
                count = location_frequency.get(location, 0)
                response += f"{i}. {location} ({count} offres)\n"
            response += "\n"
        
        # Calcul du score potentiel basé sur les vrais scores de matching
        total_skills_in_market = len(all_job_skills)
        current_matching_rate = len(matching_skills) / total_skills_in_market * 100 if total_skills_in_market > 0 else 0

        # Estimation de l'amélioration potentielle basée sur les compétences manquantes prioritaires
        high_impact_skills = missing_skills_ranked[:3]  # Top 3 des compétences manquantes
        potential_score_boost = len(high_impact_skills) * 5  # Estimation: +5% par compétence clé

        # Analyser la distribution des scores actuels
        low_scores = [score for score in current_scores if score < 50]
        medium_scores = [score for score in current_scores if 50 <= score < 75]
        high_scores = [score for score in current_scores if score >= 75]

        response += "**📈 IMPACT POTENTIEL :**\n"
        response += f"- Score moyen actuel : {avg_current_score:.1f}%\n"
        response += f"- Amélioration estimée avec top 3 compétences : +{potential_score_boost}%\n"
        response += f"- Répartition actuelle : {len(high_scores)} offres >75%, {len(medium_scores)} offres 50-75%, {len(low_scores)} offres <50%\n"
        response += f"- Nombre total de compétences analysées : {total_skills_in_market}\n\n"
        
        # Recommandations personnalisées basées sur les scores
        response += "**🎯 RECOMMANDATIONS PERSONNALISÉES :**\n"

        if avg_current_score < 40:
            response += "**🔴 PRIORITÉ HAUTE - Profil à renforcer significativement :**\n"
            response += "- Concentrez-vous sur les 2-3 premières compétences listées\n"
            response += "- Complétez votre profil avec plus de détails sur votre expérience\n"
            response += "- Ajoutez des projets concrets démontrant vos compétences\n"
        elif avg_current_score < 65:
            response += "**🟡 PRIORITÉ MOYENNE - Profil en développement :**\n"
            response += "- Développez les compétences manquantes prioritaires\n"
            response += "- Optimisez la description de vos expériences\n"
            response += "- Postulez aux offres avec >60% de correspondance\n"
        else:
            response += "**🟢 PROFIL SOLIDE - Optimisation fine :**\n"
            response += "- Affinez vos compétences spécialisées\n"
            response += "- Postulez aux meilleures offres (>70% de correspondance)\n"
            response += "- Considérez des postes plus senior\n"

        response += "\n**📋 PLAN D'ACTION :**\n"
        response += "1. Mettez à jour votre profil avec les nouvelles compétences acquises\n"
        response += "2. Ajoutez des projets démontrant ces compétences\n"
        response += "3. Consultez régulièrement les nouvelles offres\n"
        response += "4. Postulez aux offres correspondant à vos compétences actuelles\n\n"

        response += f"💪 Ces recommandations sont basées sur l'analyse IA de {len(scored_jobs)} offres d'emploi avec scores réels de matching. Bonne chance dans votre recherche !"
        
        return response
        
    except Exception as e:
        print(f"[ERROR] Erreur dans recommendation_candidate_rh: {e}")
        return f"Bonjour {display_name} ! Je rencontre une difficulté technique pour analyser votre profil et générer des recommandations. Veuillez réessayer plus tard."

def get_detailed_matching_analysis(candidate_id: str) -> Dict:
    """
    Analyse détaillée du matching avec scores réels pour un candidat

    Args:
        candidate_id (str): UUID du candidat

    Returns:
        Dict: Analyse détaillée avec scores réels
    """
    try:
        # Calculer les scores réels
        scored_jobs = calculate_candidate_job_scores(candidate_id, limit=30)

        if not scored_jobs:
            return {"error": "Aucun job trouvé pour l'analyse"}

        # Analyser les scores
        scores = [job.get('match_percentage', 0) for job in scored_jobs]
        avg_score = sum(scores) / len(scores) if scores else 0
        max_score = max(scores) if scores else 0
        min_score = min(scores) if scores else 0

        # Analyser les compétences
        all_candidate_skills = set()
        all_job_skills = set()
        all_matched_skills = set()

        for job in scored_jobs:
            candidate_skills = job.get('candidate_skills', []) or []
            job_skills = job.get('job_skills', []) or []
            matched_skills = job.get('matched_skills', []) or []

            all_candidate_skills.update([s.lower() for s in candidate_skills if s])
            all_job_skills.update([s.lower() for s in job_skills if s])
            all_matched_skills.update([s.lower() for s in matched_skills if s])

        # Compétences manquantes
        missing_skills = all_job_skills - all_candidate_skills

        # Analyser l'impact des compétences
        skill_impact = {}
        for job in scored_jobs:
            job_skills = [s.lower() for s in job.get('job_skills', []) if s]
            match_score = job.get('match_percentage', 0)

            for skill in job_skills:
                if skill not in skill_impact:
                    skill_impact[skill] = {'total_score': 0, 'count': 0, 'avg_score': 0}
                skill_impact[skill]['total_score'] += match_score
                skill_impact[skill]['count'] += 1
                skill_impact[skill]['avg_score'] = skill_impact[skill]['total_score'] / skill_impact[skill]['count']

        # Trier les compétences manquantes par impact potentiel
        missing_skills_by_impact = sorted(
            missing_skills,
            key=lambda x: skill_impact.get(x, {}).get('avg_score', 0),
            reverse=True
        )

        return {
            "candidate_id": candidate_id,
            "total_jobs_analyzed": len(scored_jobs),
            "score_statistics": {
                "average": round(avg_score, 2),
                "maximum": round(max_score, 2),
                "minimum": round(min_score, 2),
                "distribution": {
                    "excellent": len([s for s in scores if s >= 80]),
                    "good": len([s for s in scores if 60 <= s < 80]),
                    "average": len([s for s in scores if 40 <= s < 60]),
                    "poor": len([s for s in scores if s < 40])
                }
            },
            "skills_analysis": {
                "candidate_skills": list(all_candidate_skills),
                "market_skills": list(all_job_skills),
                "matched_skills": list(all_matched_skills),
                "missing_skills": missing_skills_by_impact[:10],
                "skill_coverage": round(len(all_matched_skills) / len(all_job_skills) * 100, 2) if all_job_skills else 0
            },
            "top_opportunities": [
                {
                    "job_title": job.get('job_title', ''),
                    "match_percentage": job.get('match_percentage', 0),
                    "matched_skills": job.get('matched_skills', [])
                }
                for job in sorted(scored_jobs, key=lambda x: x.get('match_percentage', 0), reverse=True)[:5]
            ]
        }

    except Exception as e:
        print(f"[ERROR] Erreur dans get_detailed_matching_analysis: {e}")
        return {"error": str(e)}

def analyze_candidate_skills_gap(candidate_id: str) -> Dict:
    """
    Analyse détaillée des lacunes de compétences d'un candidat
    
    Args:
        candidate_id (str): UUID du candidat
        
    Returns:
        Dict: Analyse détaillée des compétences
    """
    try:
        supabase = current_app.supabase
        
        # Récupérer le profil du candidat
        candidate_profile = supabase.table("candidate_profiles") \
            .select("skillner_skills, py_skills, added_skills, skills") \
            .eq("candidate_id", candidate_id) \
            .execute()
        
        if not candidate_profile.data:
            return {"error": "Profil candidat non trouvé"}
        
        profile = candidate_profile.data[0]
        
        # Combiner toutes les compétences
        candidate_skills = set()
        skill_sources = ['skillner_skills', 'py_skills', 'added_skills', 'skills']
        for source in skill_sources:
            skills_list = profile.get(source)
            if skills_list and isinstance(skills_list, list):
                candidate_skills.update([skill.strip().lower() for skill in skills_list if skill and skill.strip()])
        
        # Analyser le marché des compétences
        jobs_sample = supabase.table("jobs") \
            .select("skills, requirements") \
            .eq("is_active", True) \
            .limit(50) \
            .execute()
        
        market_skills = {}
        for job in jobs_sample.data:
            combined_skills = (job.get('skills', []) or []) + (job.get('requirements', []) or [])
            for skill in combined_skills:
                if skill and isinstance(skill, str):
                    skill_clean = skill.strip().lower()
                    if skill_clean:
                        market_skills[skill_clean] = market_skills.get(skill_clean, 0) + 1
        
        # Calculer les gaps
        missing_skills = set(market_skills.keys()) - candidate_skills
        matching_skills = candidate_skills & set(market_skills.keys())
        
        return {
            "candidate_skills": list(candidate_skills),
            "market_skills": market_skills,
            "missing_skills": sorted(missing_skills, key=lambda x: market_skills.get(x, 0), reverse=True),
            "matching_skills": sorted(matching_skills, key=lambda x: market_skills.get(x, 0), reverse=True),
            "skill_coverage": len(matching_skills) / len(market_skills) * 100 if market_skills else 0
        }
        
    except Exception as e:
        print(f"[ERROR] Erreur dans analyze_candidate_skills_gap: {e}")
        return {"error": str(e)}

def get_market_trends() -> Dict:
    """
    Analyse les tendances du marché des compétences
    
    Returns:
        Dict: Tendances du marché
    """
    try:
        supabase = current_app.supabase
        
        # Récupérer toutes les offres actives
        jobs = supabase.table("jobs") \
            .select("skills, requirements, created_at, title") \
            .eq("is_active", True) \
            .execute()
        
        skill_trends = {}
        job_titles = {}
        
        for job in jobs.data:
            # Analyser les compétences
            combined_skills = (job.get('skills', []) or []) + (job.get('requirements', []) or [])
            for skill in combined_skills:
                if skill and isinstance(skill, str):
                    skill_clean = skill.strip().lower()
                    if skill_clean:
                        skill_trends[skill_clean] = skill_trends.get(skill_clean, 0) + 1
            
            # Analyser les titres de poste
            title = job.get('title', '').strip()
            if title:
                job_titles[title] = job_titles.get(title, 0) + 1
        
        # Trier par popularité
        top_skills = sorted(skill_trends.items(), key=lambda x: x[1], reverse=True)[:20]
        top_job_titles = sorted(job_titles.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            "top_skills": top_skills,
            "top_job_titles": top_job_titles,
            "total_jobs_analyzed": len(jobs.data),
            "total_unique_skills": len(skill_trends)
        }
        
    except Exception as e:
        print(f"[ERROR] Erreur dans get_market_trends: {e}")
        return {"error": str(e)}
