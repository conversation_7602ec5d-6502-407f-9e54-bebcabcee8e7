<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chatbot</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <h1>🤖 Test Chatbot</h1>
    
    <form id="chatForm">
        <div class="form-group">
            <label for="userId">User ID:</label>
            <select id="userId">
                <option value="ed15f200-9b19-4bde-819d-f3b9a29b35e3">Candidat réel</option>
                <option value="6d2df96a-b6b2-4098-b5f3-1192fba544f7">Recruteur réel</option>
                <option value="00000000-0000-0000-0000-000000000000">Utilisateur inexistant</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="role">Rôle:</label>
            <select id="role">
                <option value="candidate">Candidate</option>
                <option value="recruiter">Recruiter</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="query">Query:</label>
            <input type="text" id="query" value="Développeur" placeholder="Votre recherche...">
        </div>
        
        <div class="form-group">
            <label for="pageContext">Page Context:</label>
            <input type="text" id="pageContext" value="homepage" placeholder="homepage">
        </div>
        
        <button type="submit">🚀 Tester</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('chatForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const userId = document.getElementById('userId').value;
            const role = document.getElementById('role').value;
            const query = document.getElementById('query').value;
            const pageContext = document.getElementById('pageContext').value;
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>🔄 Test en cours...</p>';
            
            try {
                const response = await fetch('http://127.0.0.1:5000/chat/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-User': JSON.stringify({ id: userId, role: role })
                    },
                    body: JSON.stringify({
                        query: query,
                        pageContext: pageContext
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Succès (Status: ${response.status})</h3>
                            <p><strong>Réponse:</strong></p>
                            <p>${data.response}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Erreur (Status: ${response.status})</h3>
                            <p><strong>Message:</strong> ${data.error}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>💥 Exception</h3>
                        <p><strong>Erreur:</strong> ${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
