#!/usr/bin/env python3
"""
Test de l'amélioration pour "best offers"
"""

import requests
import json
import base64

def test_best_offers_queries():
    """Test différentes variantes de recherche de meilleures offres"""
    
    url = "http://127.0.0.1:5000/chat/"
    
    # Utilisateur candidat
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"
    
    # Utilisateur recruteur  
    recruiter_id = "e714bb4b-b3f8-4343-8d5d-1e120e421b29"
    
    test_queries = [
        # Queries problématiques originales
        ("trouver best offers *", "candidate", "Query originale problématique"),
        ("best offers", "candidate", "Best offers simple"),
        ("find best jobs", "candidate", "Find best jobs anglais"),
        
        # Queries de recommandation
        ("meilleures offres", "candidate", "Meilleures offres français"),
        ("top jobs", "candidate", "Top jobs"),
        ("recommande moi des offres", "candidate", "Recommandation explicite"),
        
        # Queries techniques
        ("best developer jobs", "candidate", "Best developer jobs"),
        ("find frontend developer", "candidate", "Find frontend developer"),
        ("data scientist positions", "candidate", "Data scientist positions"),
        
        # Test recruteur
        ("best offers", "recruiter", "Recruteur - best offers"),
    ]
    
    for query, role, description in test_queries:
        print(f"🧪 {description}")
        print(f"👤 {role} | 💬 '{query}'")
        
        # Choisir l'utilisateur selon le rôle
        user_id = candidate_id if role == "candidate" else recruiter_id
        
        headers = {
            "Content-Type": "application/json"
        }
        
        data = {
            "query": query,
            "pageContext": "recherche_emploi",
            "user": {
                "id": user_id,
                "role": role
            }
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            print(f"📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                response_text = result['response']
                
                # Analyser le type de réponse
                if "**1." in response_text and "Compatibilité:" in response_text:
                    count = response_text.count("**") // 2
                    print(f"✅ Recommandations: {count} offres avec scores")
                elif "**1." in response_text:
                    count = response_text.count("**") // 2
                    print(f"✅ Résultats structurés: {count} éléments")
                elif "conseil" in response_text.lower() or len(response_text) > 500:
                    print(f"💡 Conseil/Guide détaillé")
                elif "Je n'ai pas trouvé" in response_text:
                    print(f"⚠️ Fallback - aucune offre trouvée")
                else:
                    print(f"📝 Réponse standard")
                
                # Afficher un extrait
                if len(response_text) > 150:
                    print(f"📝 {response_text[:150]}...")
                else:
                    print(f"📝 {response_text}")
                    
            else:
                error = response.json()
                print(f"❌ Erreur: {error}")
                
        except Exception as e:
            print(f"💥 Exception: {e}")
        
        print("-" * 70)

def main():
    print("🔧 TEST DES AMÉLIORATIONS 'BEST OFFERS'")
    print("=" * 70)
    
    test_best_offers_queries()
    
    print("\n🎯 RÉSUMÉ DES AMÉLIORATIONS:")
    print("✅ Nettoyage des caractères spéciaux (*)")
    print("✅ Traduction anglais -> français")
    print("✅ Détection 'best/meilleur' -> recommandations")
    print("✅ Recherche améliorée avec query nettoyée")

if __name__ == "__main__":
    main()
