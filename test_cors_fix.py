#!/usr/bin/env python3
"""
Test de la correction CORS - Support header ET body
"""

import requests
import json
import base64

def test_header_format():
    """Test avec le format header X-User (original)"""
    print("🧪 Test 1: Format header X-User (original)")
    
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": "ed15f200-9b19-4bde-819d-f3b9a29b35e3", "role": "candidate"}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": "Recommande-moi des offres",
        "pageContext": "recherche_emploi"
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Format header fonctionne!")
            print(f"Réponse: {result['response'][:100]}...")
            return True
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            return False
            
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

def test_body_format():
    """Test avec le format body user (nouveau - CORS friendly)"""
    print("\n🧪 Test 2: Format body user (nouveau - CORS friendly)")
    
    url = "http://127.0.0.1:5000/chat/"
    
    headers = {
        "Content-Type": "application/json"
        # Pas de X-User header
    }
    
    data = {
        "query": "Recommande-moi des offres",
        "pageContext": "recherche_emploi",
        "user": {
            "id": "ed15f200-9b19-4bde-819d-f3b9a29b35e3",
            "role": "candidate"
        }
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Format body fonctionne!")
            print(f"Réponse: {result['response'][:100]}...")
            return True
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            return False
            
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

def test_role_only_format():
    """Test avec seulement le role dans le body (format minimal)"""
    print("\n🧪 Test 3: Format role seulement (minimal)")
    
    url = "http://127.0.0.1:5000/chat/"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "query": "CDI vs CDD",
        "pageContext": "contrats",
        "role": "candidate"
        # Pas d'ID utilisateur - devrait échouer gracieusement
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 401:
            error = response.json()
            print("✅ Échec attendu - ID utilisateur requis")
            print(f"Message: {error.get('error', 'N/A')}")
            return True
        else:
            print(f"⚠️ Réponse inattendue: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

def test_no_auth():
    """Test sans authentification"""
    print("\n🧪 Test 4: Aucune authentification")
    
    url = "http://127.0.0.1:5000/chat/"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "query": "Hello",
        "pageContext": "test"
        # Pas d'authentification
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 401:
            error = response.json()
            print("✅ Échec attendu - Authentification requise")
            print(f"Message: {error.get('error', 'N/A')}")
            return True
        else:
            print(f"⚠️ Réponse inattendue: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

def main():
    print("🔧 TEST DE LA CORRECTION CORS")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Format header original
    if test_header_format():
        tests_passed += 1
    
    # Test 2: Format body nouveau
    if test_body_format():
        tests_passed += 1
    
    # Test 3: Format minimal (échec attendu)
    if test_role_only_format():
        tests_passed += 1
    
    # Test 4: Aucune auth (échec attendu)
    if test_no_auth():
        tests_passed += 1
    
    print(f"\n🎯 RÉSULTATS:")
    print(f"Tests réussis: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🌟 PARFAIT! Tous les formats d'authentification fonctionnent")
        print("\n📋 FORMATS SUPPORTÉS:")
        print("✅ Header X-User (base64) - Format original")
        print("✅ Body user object - Format CORS-friendly")
        print("✅ Validation d'erreurs appropriée")
        
        print("\n🚀 INSTRUCTIONS POUR LE FRONTEND:")
        print("Pour éviter les problèmes CORS, utilisez ce format:")
        print("""
{
  "query": "votre question",
  "pageContext": "recherche_emploi",
  "user": {
    "id": "user-uuid",
    "role": "candidate"
  }
}
        """)
    else:
        print("⚠️ Certains tests ont échoué - vérifiez la configuration")

if __name__ == "__main__":
    main()
