#!/usr/bin/env python3
"""
Test APIs with your specific data
Tests the exact job IDs and candidate IDs you provided
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:5000"

# Your specific job data
JOBS_TO_TEST = [
    {
        "id": "11111111-aaaa-bbbb-cccc-111111111111",
        "title": "Data Scientist Junior",
        "location": "Casablanca, Maroc",
        "skills": ["Python", "SQL", "Pandas", "Scikit-learn", "Power BI"]
    },
    {
        "id": "22222222-bbbb-cccc-dddd-222222222222", 
        "title": "Développeur OCR & Computer Vision",
        "location": "Rabat, Maroc",
        "skills": ["Python", "TensorFlow", "YOLO", "OpenCV", "PyQt5"]
    },
    {
        "id": "2b7dbbf0-10ee-4d05-b588-640a0762e3e3",
        "title": "Senior Software Engineer (Full Stack)",
        "location": "casablanca",
        "skills": ["Spring Boot", "ASP.NET", "React.js", "Docker", "Azure"]
    },
    {
        "id": "44444444-4444-4444-4444-444444444444",
        "title": "Senior Frontend Developer", 
        "location": "Paris, France (Remote)",
        "skills": ["React", "TypeScript", "JavaScript", "CSS", "Redux"]
    },
    {
        "id": "66666666-6666-6666-6666-666666666666",
        "title": "Senior Backend Engineer",
        "location": "Remote",
        "skills": ["Node.js", "Python", "AWS", "PostgreSQL", "Docker"]
    }
]

# Your specific candidate data
CANDIDATES_TO_TEST = [
    {
        "id": "4db01413-00e5-4c1c-90f7-4c03438d1dc3",
        "email": "<EMAIL>"
    },
    {
        "id": "63800628-55a2-4c33-97ce-5bcc5ee6a1bb",
        "email": "<EMAIL>",
        "name": "Taoufiq NomComplet"
    },
    {
        "id": "a482edbc-246b-4521-bf08-95d568c7900e",
        "email": "<EMAIL>",
        "name": "Mohamed ouabbi"
    },
    {
        "id": "df82c6a7-72cd-4e06-ad09-5a4d0127bca4",
        "email": "<EMAIL>",
        "name": "Mohammed Amine MAHID"
    }
]

def test_api(method, endpoint, data=None, params=None):
    """Test an API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    headers = {"Content-Type": "application/json"}
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers, params=params, timeout=10)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data, timeout=10)
        
        print(f"\n{'='*50}")
        print(f"🔍 Testing: {method} {endpoint}")
        print(f"URL: {url}")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS")
            try:
                json_response = response.json()
                if isinstance(json_response, dict):
                    if 'jobs' in json_response:
                        print(f"📊 Found {len(json_response['jobs'])} jobs")
                        if json_response['jobs']:
                            first_job = json_response['jobs'][0]
                            print(f"📝 First job: {first_job.get('title', 'No title')}")
                    elif isinstance(json_response, list):
                        print(f"📊 Found {len(json_response)} items")
                        if json_response:
                            print(f"📝 First item keys: {list(json_response[0].keys()) if json_response[0] else 'Empty'}")
                    else:
                        print(f"📝 Response keys: {list(json_response.keys())}")
                else:
                    print(f"📊 Response length: {len(str(json_response))}")
            except:
                print(f"📝 Response: {response.text[:200]}...")
        else:
            print(f"❌ FAILED - Status: {response.status_code}")
            print(f"📝 Error: {response.text}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"\n❌ ERROR testing {method} {endpoint}")
        print(f"🚨 Exception: {str(e)}")
        return False

def main():
    """Run tests with your specific data"""
    print("🚀 Testing APIs with your specific data")
    print(f"🌐 Base URL: {BASE_URL}")
    
    passed = 0
    failed = 0
    
    # Test 1: Get all jobs
    print("\n" + "="*60)
    print("📋 TESTING JOB ENDPOINTS")
    print("="*60)
    
    if test_api("GET", "/job"):
        passed += 1
    else:
        failed += 1
    
    # Test 2: Test each specific job ID
    for job in JOBS_TO_TEST:
        if test_api("GET", f"/job/{job['id']}"):
            passed += 1
        else:
            failed += 1
    
    # Test 3: Search for jobs with specific skills
    search_terms = ["Python", "React", "Docker", "TensorFlow"]
    for term in search_terms:
        if test_api("GET", "/job", params={"search": term, "limit": 5}):
            passed += 1
        else:
            failed += 1
    
    # Test 4: Filter by locations from your data
    locations = ["Remote", "Casablanca", "Paris"]
    for location in locations:
        if test_api("GET", "/job", params={"location": location, "limit": 5}):
            passed += 1
        else:
            failed += 1
    
    # Test 5: Filter by contract types
    contract_types = ["CDI", "CDD", "Freelance"]
    for contract in contract_types:
        if test_api("GET", "/job", params={"contract_type": contract, "limit": 5}):
            passed += 1
        else:
            failed += 1
    
    # Test 6: Test candidates endpoint
    print("\n" + "="*60)
    print("👥 TESTING CANDIDATE ENDPOINTS")
    print("="*60)
    
    if test_api("GET", "/candidates/"):
        passed += 1
    else:
        failed += 1
    
    # Test 7: Test recommended jobs
    print("\n" + "="*60)
    print("🎯 TESTING RECOMMENDATION ENDPOINTS")
    print("="*60)
    
    if test_api("GET", "/job/recommended"):
        passed += 1
    else:
        failed += 1
    
    # Test 8: Test pagination
    print("\n" + "="*60)
    print("📄 TESTING PAGINATION")
    print("="*60)
    
    for page in [1, 2]:
        if test_api("GET", "/job", params={"page": page, "limit": 3}):
            passed += 1
        else:
            failed += 1
    
    # Test 9: Test salary filters with your data ranges
    print("\n" + "="*60)
    print("💰 TESTING SALARY FILTERS")
    print("="*60)
    
    salary_ranges = [8000, 10000, 50000, 70000]
    for min_salary in salary_ranges:
        if test_api("GET", "/job", params={"min_salary": min_salary, "limit": 5}):
            passed += 1
        else:
            failed += 1
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    print(f"✅ Tests Passed: {passed}")
    print(f"❌ Tests Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    # Show your specific data that should be found
    print("\n" + "="*60)
    print("📋 YOUR DATA SUMMARY")
    print("="*60)
    print("🔍 Jobs to find:")
    for job in JOBS_TO_TEST:
        print(f"  • {job['title']} ({job['location']})")
    
    print("\n👥 Candidates to find:")
    for candidate in CANDIDATES_TO_TEST:
        name = candidate.get('name', 'No name')
        print(f"  • {name} ({candidate['email']})")
    
    if failed == 0:
        print("\n🎉 All tests passed! Your APIs are working perfectly with your data.")
    else:
        print(f"\n⚠️ {failed} test(s) failed. Check if the server is running and data is properly loaded.")

if __name__ == "__main__":
    main()
