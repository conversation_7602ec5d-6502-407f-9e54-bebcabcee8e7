#!/usr/bin/env python3
"""
Comprehensive API Testing Script
Tests all major endpoints with the provided sample data
"""

import requests
import json
import sys
from typing import Dict, List, Any

# Configuration
BASE_URL = "http://localhost:5000"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

# Sample data from your provided dataset
SAMPLE_JOB_IDS = [
    "11111111-aaaa-bbbb-cccc-111111111111",  # Data Scientist Junior
    "22222222-bbbb-cccc-dddd-222222222222",  # Développeur OCR & Computer Vision
    "2b7dbbf0-10ee-4d05-b588-640a0762e3e3",  # Senior Software Engineer (Full Stack)
    "44444444-4444-4444-4444-444444444444",  # Senior Frontend Developer
    "66666666-6666-6666-6666-666666666666",  # Senior Backend Engineer
]

SAMPLE_CANDIDATE_IDS = [
    "4db01413-00e5-4c1c-90f7-4c03438d1dc3",  # <EMAIL>
    "63800628-55a2-4c33-97ce-5bcc5ee6a1bb",  # <EMAIL>
    "a482edbc-246b-4521-bf08-95d568c7900e",  # <EMAIL>
    "df82c6a7-72cd-4e06-ad09-5a4d0127bca4",  # <EMAIL>
]

def test_endpoint(method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Dict[str, Any]:
    """Test a single endpoint and return results"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=HEADERS, params=params, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, headers=HEADERS, json=data, timeout=10)
        elif method.upper() == "PUT":
            response = requests.put(url, headers=HEADERS, json=data, timeout=10)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=HEADERS, timeout=10)
        else:
            return {"error": f"Unsupported method: {method}"}
        
        result = {
            "status_code": response.status_code,
            "success": response.status_code < 400,
            "url": url,
            "method": method.upper()
        }
        
        try:
            result["response"] = response.json()
        except:
            result["response"] = response.text
            
        return result
        
    except requests.exceptions.RequestException as e:
        return {
            "error": str(e),
            "success": False,
            "url": url,
            "method": method.upper()
        }

def print_test_result(test_name: str, result: Dict[str, Any]):
    """Print formatted test results"""
    print(f"\n{'='*60}")
    print(f"TEST: {test_name}")
    print(f"{'='*60}")
    print(f"URL: {result.get('url', 'N/A')}")
    print(f"Method: {result.get('method', 'N/A')}")
    print(f"Status: {'✅ SUCCESS' if result.get('success') else '❌ FAILED'}")
    print(f"Status Code: {result.get('status_code', 'N/A')}")
    
    if result.get('error'):
        print(f"Error: {result['error']}")
    
    if result.get('response'):
        response = result['response']
        if isinstance(response, dict):
            if 'jobs' in response:
                print(f"Jobs returned: {len(response['jobs'])}")
                if response['jobs']:
                    print(f"First job: {response['jobs'][0].get('title', 'No title')}")
            elif 'data' in response:
                print(f"Data items: {len(response['data']) if isinstance(response['data'], list) else 1}")
            else:
                print(f"Response keys: {list(response.keys())}")
        else:
            print(f"Response: {str(response)[:200]}...")

def main():
    """Run comprehensive API tests"""
    print("🚀 Starting Comprehensive API Testing")
    print(f"Base URL: {BASE_URL}")
    
    tests_passed = 0
    tests_failed = 0
    
    # Test 1: Get all jobs
    result = test_endpoint("GET", "/job")
    print_test_result("Get All Jobs", result)
    if result.get('success'): tests_passed += 1
    else: tests_failed += 1
    
    # Test 2: Get jobs with search filter
    result = test_endpoint("GET", "/job", params={"search": "Python", "limit": 5})
    print_test_result("Search Jobs (Python)", result)
    if result.get('success'): tests_passed += 1
    else: tests_failed += 1
    
    # Test 3: Get jobs with location filter
    result = test_endpoint("GET", "/job", params={"location": "Remote", "limit": 5})
    print_test_result("Filter Jobs by Location (Remote)", result)
    if result.get('success'): tests_passed += 1
    else: tests_failed += 1
    
    # Test 4: Get jobs with contract type filter
    result = test_endpoint("GET", "/job", params={"contract_type": "CDI", "limit": 5})
    print_test_result("Filter Jobs by Contract Type (CDI)", result)
    if result.get('success'): tests_passed += 1
    else: tests_failed += 1
    
    # Test 5: Get jobs with salary filter
    result = test_endpoint("GET", "/job", params={"min_salary": 50000, "limit": 5})
    print_test_result("Filter Jobs by Minimum Salary (50000)", result)
    if result.get('success'): tests_passed += 1
    else: tests_failed += 1
    
    # Test 6: Get specific jobs by ID
    for i, job_id in enumerate(SAMPLE_JOB_IDS[:3]):  # Test first 3 jobs
        result = test_endpoint("GET", f"/job/{job_id}")
        print_test_result(f"Get Job by ID #{i+1} ({job_id[:8]}...)", result)
        if result.get('success'): tests_passed += 1
        else: tests_failed += 1
    
    # Test 7: Get recommended jobs
    result = test_endpoint("GET", "/job/recommended")
    print_test_result("Get Recommended Jobs", result)
    if result.get('success'): tests_passed += 1
    else: tests_failed += 1
    
    # Test 8: Get all candidates
    result = test_endpoint("GET", "/candidates/")
    print_test_result("Get All Candidates", result)
    if result.get('success'): tests_passed += 1
    else: tests_failed += 1
    
    # Test 9: Test pagination
    result = test_endpoint("GET", "/job", params={"page": 1, "limit": 3})
    print_test_result("Test Pagination (Page 1, Limit 3)", result)
    if result.get('success'): tests_passed += 1
    else: tests_failed += 1
    
    result = test_endpoint("GET", "/job", params={"page": 2, "limit": 3})
    print_test_result("Test Pagination (Page 2, Limit 3)", result)
    if result.get('success'): tests_passed += 1
    else: tests_failed += 1
    
    # Test 10: Test work mode filter
    result = test_endpoint("GET", "/job", params={"work_mode": ["Remote", "Hybrid"], "limit": 5})
    print_test_result("Filter Jobs by Work Mode (Remote, Hybrid)", result)
    if result.get('success'): tests_passed += 1
    else: tests_failed += 1
    
    # Test 11: Test combined filters
    result = test_endpoint("GET", "/job", params={
        "search": "developer",
        "location": "Remote",
        "contract_type": "CDI",
        "min_salary": 40000,
        "limit": 5
    })
    print_test_result("Combined Filters Test", result)
    if result.get('success'): tests_passed += 1
    else: tests_failed += 1
    
    # Test 12: Test invalid job ID
    result = test_endpoint("GET", "/job/invalid-job-id")
    print_test_result("Test Invalid Job ID", result)
    # This should fail with 404, so we count it as success if status is 404
    if result.get('status_code') == 404: tests_passed += 1
    else: tests_failed += 1
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Tests Passed: {tests_passed}")
    print(f"❌ Tests Failed: {tests_failed}")
    print(f"📈 Success Rate: {(tests_passed/(tests_passed+tests_failed)*100):.1f}%")
    
    if tests_failed == 0:
        print("\n🎉 All tests passed! Your APIs are working correctly.")
    else:
        print(f"\n⚠️  {tests_failed} test(s) failed. Please check the server logs.")
    
    return tests_failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
