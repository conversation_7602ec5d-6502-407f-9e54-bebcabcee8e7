#!/usr/bin/env python3
"""
Test du chatbot avec différentes queries
"""

import requests
import json
import base64

def test_query(query, page_context, description):
    """Test une query spécifique"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": "4db01413-00e5-4c1c-90f7-4c03438d1dc3", "role": "candidate"}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"🧪 {description}")
    print(f"Query: {query}")
    print(f"Context: {page_context}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès!")
            response_text = result['response']
            if len(response_text) > 200:
                print(f"Réponse: {response_text[:200]}...")
                print(f"[Réponse complète: {len(response_text)} caractères]")
            else:
                print(f"Réponse: {response_text}")
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")
    
    print("\n" + "="*60 + "\n")

def main():
    print("🎯 Test du chatbot avec différentes queries")
    print("="*60)
    
    # Test 1: Recherche d'emploi spécifique
    test_query(
        "Développeur Frontend React", 
        "recherche_emploi",
        "Test 1: Recherche Frontend React"
    )
    
    # Test 2: Recherche d'emploi générale
    test_query(
        "Data Scientist", 
        "recherche_emploi",
        "Test 2: Recherche Data Scientist"
    )
    
    # Test 3: Conseil CV
    test_query(
        "Comment améliorer mon CV pour un poste de développeur ?", 
        "conseil",
        "Test 3: Conseil CV"
    )
    
    # Test 4: Préparation entretien
    test_query(
        "Comment me préparer pour un entretien technique ?", 
        "entretien",
        "Test 4: Préparation entretien"
    )
    
    # Test 5: Question sur les compétences
    test_query(
        "Quelles sont les compétences les plus demandées en 2024 ?", 
        "skills",
        "Test 5: Compétences 2024"
    )
    
    # Test 6: Question sur les contrats (fallback)
    test_query(
        "Quelle est la différence entre freelance et CDI ?", 
        "contrats",
        "Test 6: Freelance vs CDI (fallback)"
    )
    
    # Test 7: Recherche très spécifique
    test_query(
        "Ingénieur DevOps AWS", 
        "recherche_emploi",
        "Test 7: DevOps AWS spécifique"
    )
    
    # Test 8: Question générale
    test_query(
        "Comment trouver un emploi rapidement ?", 
        "conseil",
        "Test 8: Conseil général emploi"
    )

if __name__ == "__main__":
    main()
