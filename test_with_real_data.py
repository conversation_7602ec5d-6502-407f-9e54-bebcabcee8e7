#!/usr/bin/env python3
"""
Test avec de vraies données de la base (si disponible)
"""

import sys
import os
import json

# Ajouter le répertoire racine au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """Test de connexion à la base de données"""
    print("🔌 Test de connexion à la base de données...")
    
    try:
        # Essayer d'importer et initialiser l'app
        from app import create_app
        
        app = create_app()
        
        with app.app_context():
            supabase = app.supabase
            
            # Test simple: récupérer quelques candidats
            candidates = supabase.table("candidates").select("id, full_name").limit(3).execute()
            
            if candidates.data:
                print(f"✅ Connexion réussie - {len(candidates.data)} candidats trouvés")
                for candidate in candidates.data:
                    print(f"   - {candidate.get('full_name', 'Sans nom')} ({candidate.get('id', 'Sans ID')[:8]}...)")
                return True, candidates.data
            else:
                print("⚠️  Connexion réussie mais aucun candidat trouvé")
                return True, []
                
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False, []

def test_candidate_profiles():
    """Test de récupération des profils candidats"""
    print("\n👤 Test des profils candidats...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.app_context():
            supabase = app.supabase
            
            # Récupérer quelques profils avec compétences
            profiles = supabase.table("candidate_profiles") \
                .select("candidate_id, skillner_skills, py_skills, added_skills, skills") \
                .limit(3).execute()
            
            if profiles.data:
                print(f"✅ {len(profiles.data)} profils trouvés")
                
                for profile in profiles.data:
                    candidate_id = profile.get('candidate_id', 'Unknown')[:8]
                    
                    # Compter les compétences
                    total_skills = 0
                    skill_sources = ['skillner_skills', 'py_skills', 'added_skills', 'skills']
                    
                    for source in skill_sources:
                        skills = profile.get(source, [])
                        if skills and isinstance(skills, list):
                            total_skills += len(skills)
                    
                    print(f"   - Candidat {candidate_id}... : {total_skills} compétences")
                
                return True, profiles.data
            else:
                print("⚠️  Aucun profil trouvé")
                return True, []
                
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False, []

def test_jobs_data():
    """Test de récupération des offres d'emploi"""
    print("\n💼 Test des offres d'emploi...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.app_context():
            supabase = app.supabase
            
            # Récupérer quelques jobs actifs
            jobs = supabase.table("jobs") \
                .select("id, title, skills, requirements, is_active") \
                .eq("is_active", True) \
                .limit(5).execute()
            
            if jobs.data:
                print(f"✅ {len(jobs.data)} offres actives trouvées")
                
                for job in jobs.data:
                    job_id = job.get('id', 'Unknown')[:8]
                    title = job.get('title', 'Sans titre')
                    skills = job.get('skills', []) or []
                    requirements = job.get('requirements', []) or []
                    total_skills = len(skills) + len(requirements)
                    
                    print(f"   - {title} ({job_id}...) : {total_skills} compétences")
                
                return True, jobs.data
            else:
                print("⚠️  Aucune offre active trouvée")
                return True, []
                
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False, []

def test_matching_service():
    """Test du service de matching"""
    print("\n🔄 Test du service de matching...")
    
    try:
        from app import create_app
        from app.services.matching_service import get_matching_service
        
        app = create_app()
        
        with app.app_context():
            # Essayer d'obtenir le service de matching
            matching_service = get_matching_service()
            
            if matching_service:
                print("✅ Service de matching accessible")
                
                # Vérifier les méthodes disponibles
                methods = [method for method in dir(matching_service) if not method.startswith('_')]
                print(f"✅ Méthodes disponibles: {len(methods)}")
                
                # Chercher la méthode de matching candidat-jobs
                if hasattr(matching_service, 'match_candidate_to_jobs'):
                    print("✅ Méthode match_candidate_to_jobs trouvée")
                    return True
                else:
                    print("⚠️  Méthode match_candidate_to_jobs non trouvée")
                    print(f"   Méthodes disponibles: {methods[:5]}...")
                    return False
            else:
                print("❌ Service de matching non accessible")
                return False
                
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_full_recommendation_logic():
    """Test complet de la logique de recommandation avec vraies données"""
    print("\n🎯 Test complet de la logique de recommandation...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.app_context():
            supabase = app.supabase
            
            # Récupérer un candidat avec profil
            candidate = supabase.table("candidates").select("id, full_name").limit(1).execute()
            
            if not candidate.data:
                print("⚠️  Aucun candidat trouvé pour le test")
                return False
            
            candidate_id = candidate.data[0]['id']
            candidate_name = candidate.data[0].get('full_name', 'Test User')
            
            print(f"✅ Test avec candidat: {candidate_name} ({candidate_id[:8]}...)")
            
            # Récupérer le profil
            profile = supabase.table("candidate_profiles") \
                .select("skillner_skills, py_skills, added_skills, skills") \
                .eq("candidate_id", candidate_id) \
                .execute()
            
            if not profile.data:
                print("⚠️  Aucun profil trouvé pour ce candidat")
                return False
            
            # Analyser les compétences
            profile_data = profile.data[0]
            candidate_skills = set()
            
            skill_sources = ['skillner_skills', 'py_skills', 'added_skills', 'skills']
            for source in skill_sources:
                skills_list = profile_data.get(source)
                if skills_list and isinstance(skills_list, list):
                    candidate_skills.update([skill.strip().lower() for skill in skills_list if skill and skill.strip()])
            
            print(f"✅ Compétences candidat: {len(candidate_skills)} trouvées")
            if candidate_skills:
                print(f"   Exemples: {list(candidate_skills)[:5]}")
            
            # Récupérer quelques jobs
            jobs = supabase.table("jobs") \
                .select("id, title, skills, requirements") \
                .eq("is_active", True) \
                .limit(5).execute()
            
            if jobs.data:
                print(f"✅ {len(jobs.data)} offres analysées")
                
                # Simuler l'analyse de matching
                job_skills_frequency = {}
                for job in jobs.data:
                    job_skills = (job.get('skills', []) or []) + (job.get('requirements', []) or [])
                    for skill in job_skills:
                        if skill and isinstance(skill, str):
                            skill_clean = skill.strip().lower()
                            if skill_clean:
                                job_skills_frequency[skill_clean] = job_skills_frequency.get(skill_clean, 0) + 1
                
                # Analyser les correspondances
                all_job_skills = set(job_skills_frequency.keys())
                matching_skills = candidate_skills & all_job_skills
                missing_skills = all_job_skills - candidate_skills
                
                print(f"✅ Compétences correspondantes: {len(matching_skills)}")
                print(f"✅ Compétences manquantes: {len(missing_skills)}")
                
                if matching_skills:
                    print(f"   Correspondances: {list(matching_skills)[:3]}")
                
                if missing_skills:
                    # Trier par fréquence
                    missing_ranked = sorted(missing_skills, key=lambda x: job_skills_frequency.get(x, 0), reverse=True)
                    print(f"   Manquantes (top 3): {missing_ranked[:3]}")
                
                return True
            else:
                print("⚠️  Aucune offre trouvée")
                return False
                
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🧪 Test avec données réelles")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 5
    
    # Test de connexion
    db_ok, candidates = test_database_connection()
    if db_ok:
        tests_passed += 1
    
    # Test des profils
    profiles_ok, profiles = test_candidate_profiles()
    if profiles_ok:
        tests_passed += 1
    
    # Test des jobs
    jobs_ok, jobs = test_jobs_data()
    if jobs_ok:
        tests_passed += 1
    
    # Test du service de matching
    matching_ok = test_matching_service()
    if matching_ok:
        tests_passed += 1
    
    # Test complet si tout est OK
    if db_ok and profiles_ok and jobs_ok:
        full_test_ok = test_full_recommendation_logic()
        if full_test_ok:
            tests_passed += 1
    else:
        print("\n⚠️  Test complet ignoré - données insuffisantes")
    
    print("\n" + "=" * 50)
    print(f"📊 Résultats: {tests_passed}/{total_tests} tests réussis")
    
    if tests_passed >= 3:
        print("🎉 Tests de base réussis !")
        print("\n💡 Recommandations:")
        if candidates and profiles and jobs:
            print("✅ Vous pouvez tester la fonction complète avec le serveur Flask")
        else:
            print("⚠️  Ajoutez des données (candidats, profils, jobs) pour des tests complets")
    else:
        print("❌ Tests de base échoués")
        print("Vérifiez la configuration de la base de données")

if __name__ == "__main__":
    main()
