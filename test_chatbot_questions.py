#!/usr/bin/env python3
"""
Test du chatbot avec différentes questions réalistes
"""

import requests
import json
import base64

def encode_user_header(user_id, role):
    """Encode les données utilisateur en base64 pour le header"""
    user_data = {"id": user_id, "role": role}
    user_json = json.dumps(user_data)
    encoded = base64.b64encode(user_json.encode('utf-8')).decode('utf-8')
    return encoded

def test_question(user_id, role, query, page_context, question_num, total_questions):
    """Test une question spécifique"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header en base64
    x_user_encoded = encode_user_header(user_id, role)
    
    headers = {
        "Content-Type": "application/json",
        "X-User": x_user_encoded
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"🤖 Test {question_num}/{total_questions}:")
    print(f"📝 Question: {query}")
    print(f"📍 Contexte: {page_context}")
    
    try:
        response = requests.post(url, headers=headers, json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Status: {response.status_code}")
            print(f"💬 Réponse: {result['response'][:200]}...")
            if len(result['response']) > 200:
                print(f"    [Réponse complète: {len(result['response'])} caractères]")
        else:
            error = response.json()
            print(f"❌ Status: {response.status_code}")
            print(f"🚨 Erreur: {error}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")
    
    print("\n" + "="*80 + "\n")

def main():
    print("🎯 Test du chatbot avec questions réalistes")
    print("="*80)
    
    # ID d'un candidat réel (remplace par un ID valide de ta base)
    user_id = "df82c6a7-72cd-4e06-ad09-5a4d0127bca4"  # Assure-toi que cet ID existe
    role = "candidate"
    
    # Questions réalistes pour tester le chatbot
    questions = [
        {
            "query": "Comment améliorer mon CV pour un poste de data scientist ?",
            "pageContext": "conseil"
        },
        {
            "query": "Que dois-je mettre dans une lettre de motivation pour une startup tech ?",
            "pageContext": "lettre_motivation"
        },
        {
            "query": "Quelle est la différence entre un CDI et un CDD ?",
            "pageContext": "contrats"
        },
        {
            "query": "Comment me préparer à un entretien développeur backend ?",
            "pageContext": "entretien"
        },
        {
            "query": "Est-ce qu'il faut apprendre Docker pour trouver un job de développeur ?",
            "pageContext": "skills"
        },
        {
            "query": "Développeur Python",
            "pageContext": "recherche_emploi"
        },
        {
            "query": "Frontend React",
            "pageContext": "recherche_emploi"
        }
    ]
    
    total_questions = len(questions)
    
    for i, question in enumerate(questions, 1):
        test_question(
            user_id, 
            role, 
            question["query"], 
            question["pageContext"], 
            i, 
            total_questions
        )
    
    print("🎉 Tests terminés!")

if __name__ == "__main__":
    main()
