<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Offers Fetch</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .offers-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 10px;
        }
        .offer-item {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
        }
        .offer-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Offers Fetch Request</h1>
        <p>This page tests the exact fetch request you provided to the <code>/offers</code> endpoint.</p>
        
        <button onclick="testOffersFetch()">🚀 Test Fetch Request</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function testOffersFetch() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result info">🔄 Testing fetch request...</div>';
            
            try {
                // Your exact fetch request
                const response = await fetch("http://localhost:5000/offers?", {
                    "headers": {
                        "accept": "*/*",
                        "accept-language": "fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7,ar;q=0.6,en-AU;q=0.5",
                        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
                        "sec-ch-ua-mobile": "?0",
                        "sec-ch-ua-platform": "\"Windows\"",
                        "sec-fetch-dest": "empty",
                        "sec-fetch-mode": "cors",
                        "sec-fetch-site": "same-site"
                    },
                    "referrer": "http://localhost:3000/",
                    "body": null,
                    "method": "GET",
                    "mode": "cors",
                    "credentials": "omit"
                });

                const data = await response.json();
                
                let resultHTML = `<div class="result success">
✅ SUCCESS!

📊 Response Status: ${response.status}
📋 Response Headers:
${Object.entries(response.headers).map(([key, value]) => `   ${key}: ${value}`).join('\n')}

📄 Response Data:
   Type: ${Array.isArray(data) ? 'Array' : typeof data}
   ${Array.isArray(data) ? `Length: ${data.length} offers` : ''}

🎯 Test Result: Your fetch request works perfectly!
</div>`;

                if (Array.isArray(data) && data.length > 0) {
                    resultHTML += `<div class="offers-list">
<h3>📋 Offers Preview (first 3):</h3>`;
                    
                    data.slice(0, 3).forEach((offer, index) => {
                        resultHTML += `<div class="offer-item">
<strong>Offer ${index + 1}:</strong> ${offer.title || 'No title'}
<br><strong>Company:</strong> ${offer.company_id || 'N/A'}
<br><strong>Location:</strong> ${offer.location || 'N/A'}
<br><strong>Created:</strong> ${offer.created_at || 'N/A'}
</div>`;
                    });
                    
                    resultHTML += '</div>';
                }
                
                resultDiv.innerHTML = resultHTML;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">
❌ ERROR!

🚨 Error Message: ${error.message}

🔍 Possible Issues:
- Server not running (start with: python run.py)
- CORS configuration issue
- Network connectivity problem

💡 Check the browser console for more details.
</div>`;
                console.error('Fetch error:', error);
            }
        }
        
        // Auto-run test when page loads
        window.addEventListener('load', () => {
            setTimeout(testOffersFetch, 1000);
        });
    </script>
</body>
</html>
