from flask import Flask
from supabase import create_client
import os
from dotenv import load_dotenv
from flask_cors import CORS
from flask import Flask, send_from_directory

# === SkillNer Setup ===
import spacy
from spacy.matcher import PhraseMatcher
from skillNer.general_params import SKILL_DB
from skillNer.skill_extractor_class import SkillExtractor

load_dotenv()

def create_app():
    app = Flask(__name__)

    # Supabase configuration
    app.supabase = create_client(
        os.getenv("SUPABASE_URL"),
        os.getenv("SUPABASE_KEY")
    )

    # Configure upload folder for local CV storage
    app.config['UPLOAD_FOLDER'] = os.path.join(app.root_path, 'uploads', 'cvs')
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    # Serve static files from the uploads/cvs directory
    app.add_url_rule('/uploads/cvs/<path:filename>',
                     endpoint='uploaded_cvs',
                     view_func=lambda filename: send_from_directory(app.config['UPLOAD_FOLDER'], filename))

    # === Initialize SkillNer once ===
    nlp = spacy.load("en_core_web_lg")
    app.skill_extractor = SkillExtractor(nlp, SKILL_DB, PhraseMatcher)
    app.SKILL_DB=SKILL_DB
    CORS(app, resources={r"/*": {"origins": "http://localhost:3000"}}, supports_credentials=True)

    # Register blueprints
    from .routes.auth import auth_bp
    from .routes.cv import cv_bp
    from .routes.profile import profile_bp
    from .routes.job import job_bp
    from .routes.parser import parser_bp
    from .routes.application import application_bp
    from .routes.ai_matching_routes import ai_matching_bp
    from app.routes.recruiter import recruiter_bp
    from app.routes.candidate import candidate_bp
    from app.routes.company import company_bp
    from app.routes.offer import offer_bp
    from app.routes.interview import interview_bp
    from app.routes.notification import notification_bp
    from app.routes.dashboard import dashboard_bp
    from app.routes.chat import rag_bp
   
 
    app.register_blueprint(recruiter_bp, url_prefix="/recruiters")
    app.register_blueprint(candidate_bp, url_prefix="/candidates")
    app.register_blueprint(company_bp, url_prefix="/company")
    app.register_blueprint(offer_bp, url_prefix="/offers")
    app.register_blueprint(application_bp, url_prefix="/application")
    app.register_blueprint(interview_bp, url_prefix="/interviews")
    app.register_blueprint(notification_bp, url_prefix="/notifications")
    app.register_blueprint(dashboard_bp, url_prefix="/api")
    app.register_blueprint(auth_bp, url_prefix="/auth")
    app.register_blueprint(cv_bp, url_prefix="/cv")
    app.register_blueprint(profile_bp, url_prefix="/profile")
    app.register_blueprint(parser_bp, url_prefix="/parser")
    app.register_blueprint(job_bp, url_prefix="/job")
    app.register_blueprint(rag_bp, url_prefix="/chat")

    app.register_blueprint(ai_matching_bp)

    return app
