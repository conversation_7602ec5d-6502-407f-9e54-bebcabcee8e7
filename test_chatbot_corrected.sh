#!/bin/bash

# Script bash corrigé pour tester le chatbot avec header X-User base64

URL="http://127.0.0.1:5000/chat/"
HEADER="Content-Type: application/json"

# Utiliser un ID valide de ta base de données Supabase
USER_JSON='{"id":"4db01413-00e5-4c1c-90f7-4c03438d1dc3","role":"candidate"}'

# CORRECTION PRINCIPALE: Ajouter | tr -d '\n' pour supprimer les retours à la ligne
USER_B64=$(echo -n "$USER_JSON" | base64 | tr -d '\n')

echo "=== Informations de debug ==="
echo "USER_JSON: $USER_JSON"
echo "USER_B64: $USER_B64"
echo "Longueur USER_B64: ${#USER_B64}"
echo ""

# Test de validation simple
echo "=== Test de validation de l'encodage ==="
if command -v python3 &> /dev/null; then
    echo "Python3 disponible - Test de decodage..."
    echo "$USER_B64" | python3 -c "import base64, sys, json; print('Encodage valide:', json.loads(base64.b64decode(sys.stdin.read().strip()).decode('utf-8')))" 2>/dev/null || echo "Erreur de decodage"
else
    echo "Python3 non disponible pour la validation"
fi
echo ""

# Questions de test (sans accents pour eviter les problemes bash)
declare -a QUERIES=(
  '{"query": "Comment ameliorer mon CV pour un poste de data scientist ?", "pageContext": "conseil"}'
  '{"query": "Que dois-je mettre dans une lettre de motivation pour une startup tech ?", "pageContext": "lettre_motivation"}'
  '{"query": "Quelle est la difference entre un CDI et un CDD ?", "pageContext": "contrats"}'
  '{"query": "Comment me preparer a un entretien developpeur backend ?", "pageContext": "entretien"}'
  '{"query": "Est-ce qu il faut apprendre Docker pour trouver un job de developpeur ?", "pageContext": "skills"}'
  '{"query": "Developpeur Python", "pageContext": "recherche_emploi"}'
  '{"query": "Frontend React", "pageContext": "recherche_emploi"}'
)

echo "=== Debut des tests du chatbot ==="
echo "=================================="

# Boucle de test pour chaque question
for i in "${!QUERIES[@]}"
do
  query="${QUERIES[$i]}"
  test_num=$((i+1))
  total=${#QUERIES[@]}

  echo ""
  echo "Test $test_num/$total:"
  echo "Question: $query"
  echo ""

  # Faire la requete curl avec le header X-User base64 corrige
  response=$(curl -s -X POST "$URL" \
    -H "$HEADER" \
    -H "X-User: $USER_B64" \
    -d "$query")

  echo "Reponse du serveur:"

  # Afficher la reponse (simplifie pour eviter les problemes de syntaxe)
  if command -v python3 &> /dev/null; then
    echo "$response" | python3 -c "import json, sys; data = json.loads(sys.stdin.read()); print('Succes!' if 'response' in data else 'Erreur: ' + str(data.get('error', 'Inconnue'))); print('Reponse:', data.get('response', '')[:100] + '...' if data.get('response') and len(data.get('response', '')) > 100 else data.get('response', ''))" 2>/dev/null || echo "$response"
  else
    echo "$response"
  fi

  echo ""
  echo "=================================="
done

echo ""
echo "Tests termines!"
echo ""
echo "Notes:"
echo "- Si tu vois des erreurs 404 'Candidate not found', remplace l'ID dans USER_JSON"
echo "- Si tu vois des erreurs 'Invalid base64', le probleme est resolu avec tr -d '\n'"
echo "- Les reponses devraient commencer par 'Bonjour [nom]!' maintenant"
