#!/bin/bash

# Script bash corrigé pour tester le chatbot avec base64

URL="http://127.0.0.1:5000/chat/"
HEADER="Content-Type: application/json"

# Utiliser un ID valide de ta base de données
USER_JSON='{"id":"4db01413-00e5-4c1c-90f7-4c03438d1dc3","role":"candidate"}'

# CORRECTION: Utiliser tr -d '\n' pour supprimer les retours à la ligne
USER_B64=$(echo -n "$USER_JSON" | base64 | tr -d '\n')

echo "🔍 Debug info:"
echo "USER_JSON: $USER_JSON"
echo "USER_B64: $USER_B64"
echo "USER_B64 length: ${#USER_B64}"
echo ""

# Test de décodage pour vérifier que l'encodage est correct
echo "🧪 Test de décodage:"
if command -v python3 &> /dev/null; then
    DECODED=$(echo "$USER_B64" | python3 -c "import base64, sys, json; print(json.loads(base64.b64decode(sys.stdin.read().strip()).decode('utf-8')))" 2>/dev/null)
    echo "Décodé: $DECODED"
else
    echo "Python3 non disponible pour le test de décodage"
fi
echo ""

declare -a QUERIES=(
  '{"query": "Comment améliorer mon CV pour un poste de data scientist ?", "pageContext": "conseil"}'
  '{"query": "Que dois-je mettre dans une lettre de motivation pour une startup tech ?", "pageContext": "lettre_motivation"}'
  '{"query": "Développeur Python", "pageContext": "recherche_emploi"}'
  '{"query": "Frontend React", "pageContext": "recherche_emploi"}'
)

echo "🚀 Début des tests du chatbot"
echo "================================"

for i in "${!QUERIES[@]}"
do
  query="${QUERIES[$i]}"
  test_num=$((i+1))
  total=${#QUERIES[@]}
  
  echo ""
  echo "🤖 Test $test_num/$total:"
  echo ">>> Question: $query"
  echo ""
  
  # Faire la requête curl
  response=$(curl -s -X POST "$URL" \
    -H "$HEADER" \
    -H "X-User: $USER_B64" \
    -d "$query")
  
  echo ">>> Réponse:"
  
  # Essayer de formater la réponse JSON si possible
  if command -v python3 &> /dev/null; then
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
  else
    echo "$response"
  fi
  
  echo ""
  echo "=" * 60
done

echo ""
echo "🎉 Tests terminés!"
