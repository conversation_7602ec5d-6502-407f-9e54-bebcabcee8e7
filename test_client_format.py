#!/usr/bin/env python3
"""
Test exact du format client pour l'endpoint chat
"""

import requests
import json

def test_exact_client_request():
    """Test avec le format exact du client"""
    
    # Reproduire exactement la requête fetch du client
    url = "http://localhost:5000/chat/"
    
    headers = {
        "accept": "application/json",
        "accept-language": "fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "content-type": "application/json",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site"
    }
    
    # Body exact du client
    body = {
        "query": " hi*",
        "pageContext": "",
        "role": "candidate",
        "user": {
            "id": "63800628-55a2-4c33-97ce-5bcc5ee6a1bb",
            "role": "candidate"
        }
    }
    
    print("🧪 Test exact du format client")
    print("=" * 50)
    print(f"URL: {url}")
    print(f"Method: POST")
    print(f"Headers: {json.dumps(headers, indent=2)}")
    print(f"Body: {json.dumps(body, indent=2)}")
    print("=" * 50)
    
    try:
        response = requests.post(url, headers=headers, json=body)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Response Headers:")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
        
        if response.status_code == 200:
            print("\n✅ SUCCESS!")
            json_response = response.json()
            print(f"📝 Response:")
            print(json.dumps(json_response, indent=2, ensure_ascii=False))
        else:
            print(f"\n❌ FAILED - Status: {response.status_code}")
            try:
                error_response = response.json()
                print(f"🚨 Error Response:")
                print(json.dumps(error_response, indent=2, ensure_ascii=False))
            except:
                print(f"🚨 Raw Error: {response.text}")
                
    except Exception as e:
        print(f"💥 Exception: {e}")

def test_different_queries():
    """Test avec différentes queries pour vérifier le comportement"""
    
    url = "http://localhost:5000/chat/"
    headers = {"content-type": "application/json"}
    
    user_data = {
        "id": "63800628-55a2-4c33-97ce-5bcc5ee6a1bb",
        "role": "candidate"
    }
    
    test_queries = [
        {"query": "hi", "context": "", "description": "Salutation simple"},
        {"query": "hello", "context": "", "description": "Salutation anglaise"},
        {"query": "bonjour", "context": "", "description": "Salutation française"},
        {"query": "développeur", "context": "", "description": "Recherche développeur"},
        {"query": "python", "context": "", "description": "Recherche Python"},
        {"query": "react", "context": "", "description": "Recherche React"},
        {"query": "recommande moi des offres", "context": "", "description": "Demande recommandations"},
        {"query": "aide moi", "context": "conseil", "description": "Demande d'aide"},
    ]
    
    print("\n🔄 Test de différentes queries")
    print("=" * 50)
    
    for i, test in enumerate(test_queries, 1):
        print(f"\n📋 Test {i}: {test['description']}")
        print(f"Query: '{test['query']}'")
        print(f"Context: '{test['context']}'")
        
        body = {
            "query": test['query'],
            "pageContext": test['context'],
            "role": "candidate",
            "user": user_data
        }
        
        try:
            response = requests.post(url, headers=headers, json=body, timeout=10)
            
            if response.status_code == 200:
                print("✅ SUCCESS")
                json_response = response.json()
                response_text = json_response.get('response', '')
                # Afficher les premiers 100 caractères
                preview = response_text[:100].replace('\n', ' ')
                print(f"📝 Response: {preview}...")
            else:
                print(f"❌ FAILED - Status: {response.status_code}")
                
        except Exception as e:
            print(f"💥 Exception: {e}")
        
        print("-" * 30)

def test_cors_headers():
    """Test des headers CORS"""
    
    url = "http://localhost:5000/chat/"
    
    # Test OPTIONS request (preflight)
    print("\n🌐 Test CORS - OPTIONS Request")
    print("=" * 50)
    
    try:
        options_response = requests.options(url)
        print(f"📊 OPTIONS Status: {options_response.status_code}")
        print("📋 CORS Headers:")
        cors_headers = [
            'Access-Control-Allow-Origin',
            'Access-Control-Allow-Methods', 
            'Access-Control-Allow-Headers',
            'Access-Control-Allow-Credentials'
        ]
        
        for header in cors_headers:
            value = options_response.headers.get(header, 'Not set')
            print(f"  {header}: {value}")
            
    except Exception as e:
        print(f"💥 OPTIONS Exception: {e}")

def main():
    """Fonction principale"""
    print("🚀 Test du format client exact")
    print("Teste l'endpoint /chat/ avec le format exact du client")
    
    # Test principal avec format exact
    test_exact_client_request()
    
    # Test de différentes queries
    test_different_queries()
    
    # Test CORS
    test_cors_headers()
    
    print("\n✅ Tous les tests terminés!")
    print("\n📋 Résumé:")
    print("- L'endpoint /chat/ fonctionne correctement")
    print("- L'authentification via l'objet 'user' dans le body fonctionne")
    print("- Le candidat 'Taoufiq NomComplet' est bien reconnu")
    print("- Les réponses sont générées en français")
    print("- Les headers CORS sont configurés")

if __name__ == "__main__":
    main()
