#!/usr/bin/env python3
"""
Test rapide des recommandations optimisées
"""

import requests
import json
import base64

def test_recommendation(user_id, role, query, page_context, description):
    """Test une recommandation spécifique"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": user_id, "role": role}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"🎯 {description}")
    print(f"Query: {query}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès!")
            response_text = result['response']
            
            # Compter les recommandations
            if "**1." in response_text:
                recommendation_count = response_text.count("**") // 2
                print(f"📊 Nombre de recommandations: {recommendation_count}")
            
            if len(response_text) > 300:
                print(f"📝 Réponse: {response_text[:300]}...")
            else:
                print(f"📝 Réponse: {response_text}")
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")
    
    print("\n" + "="*60 + "\n")

def main():
    print("🚀 Test rapide des recommandations optimisées")
    print("="*60)
    
    # IDs des utilisateurs
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"
    
    # Test 1: Candidat demande des recommandations
    test_recommendation(
        candidate_id, 
        "candidate",
        "Recommande-moi des offres d'emploi", 
        "recherche_emploi",
        "Test 1: Candidat - recommandations optimisées"
    )
    
    # Test 2: Candidat avec autre formulation
    test_recommendation(
        candidate_id, 
        "candidate",
        "Quelles opportunités correspondent à mon profil ?", 
        "recherche_emploi",
        "Test 2: Candidat - opportunités profil"
    )

if __name__ == "__main__":
    main()
