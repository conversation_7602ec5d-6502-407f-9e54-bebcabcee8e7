#!/usr/bin/env python3
"""
Test complet des conseils RH
"""

import requests
import json
import base64

def test_conseil_rh(user_id, role, query, page_context, description):
    """Test un conseil RH spécifique"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": user_id, "role": role}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"💡 {description}")
    print(f"👤 {role.upper()} | 💬 {query}")
    print(f"📋 Context: {page_context}")
    print("-" * 80)
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            response_text = result['response']
            
            print(f"✅ SUCCÈS!")
            print(f"📏 Longueur: {len(response_text)} caractères")
            
            # Analyser le type de conseil
            if any(keyword in response_text.lower() for keyword in ["conseil", "recommande", "suggère"]):
                print(f"🎯 Type: Conseil personnalisé")
            elif any(keyword in response_text.lower() for keyword in ["cdi", "cdd", "contrat"]):
                print(f"📄 Type: Information contractuelle")
            elif any(keyword in response_text.lower() for keyword in ["compétence", "skill", "formation"]):
                print(f"🎓 Type: Guidance compétences")
            elif any(keyword in response_text.lower() for keyword in ["entretien", "interview"]):
                print(f"🤝 Type: Préparation entretien")
            
            # Extraire salutation
            if "Bonjour" in response_text:
                greeting = response_text.split('\n')[0]
                print(f"👋 Salutation: {greeting}")
            
            # Compter les points de conseil
            bullet_points = response_text.count('*') + response_text.count('-') + response_text.count('•')
            if bullet_points > 0:
                print(f"📝 Points de conseil: {bullet_points}")
            
            print(f"\n📖 RÉPONSE COMPLÈTE:")
            print("=" * 80)
            print(response_text)
            print("=" * 80)
            
        else:
            error = response.json()
            print(f"❌ ERREUR: {error}")
            
    except Exception as e:
        print(f"💥 EXCEPTION: {e}")
    
    print("\n" + "🔚" * 40 + "\n")

def main():
    print("💼 TEST COMPLET DES CONSEILS RH")
    print("=" * 80)
    
    # IDs des utilisateurs
    recruiter_id = "e714bb4b-b3f8-4343-8d5d-1e120e421b29"  # John Doe
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"  # Mohamed ouabbi
    
    print("🔥 SECTION 1: CONSEILS CANDIDATS - CV & CANDIDATURE")
    print("=" * 80)
    
    # Test 1: Amélioration CV
    test_conseil_rh(
        candidate_id, 
        "candidate",
        "Comment améliorer mon CV pour un poste de développeur ?", 
        "conseil",
        "Amélioration CV développeur"
    )
    
    # Test 2: Lettre de motivation
    test_conseil_rh(
        candidate_id, 
        "candidate",
        "Comment rédiger une lettre de motivation efficace ?", 
        "lettre_motivation",
        "Rédaction lettre de motivation"
    )
    
    # Test 3: Portfolio
    test_conseil_rh(
        candidate_id, 
        "candidate",
        "Comment créer un portfolio professionnel ?", 
        "conseil",
        "Création portfolio"
    )
    
    print("🔥 SECTION 2: CONSEILS CANDIDATS - ENTRETIENS")
    print("=" * 80)
    
    # Test 4: Préparation entretien
    test_conseil_rh(
        candidate_id, 
        "candidate",
        "Comment me préparer pour un entretien technique ?", 
        "entretien",
        "Préparation entretien technique"
    )
    
    # Test 5: Questions à poser
    test_conseil_rh(
        candidate_id, 
        "candidate",
        "Quelles questions poser à la fin d'un entretien ?", 
        "entretien",
        "Questions candidat en entretien"
    )
    
    # Test 6: Négociation salaire
    test_conseil_rh(
        candidate_id, 
        "candidate",
        "Comment négocier mon salaire ?", 
        "conseil",
        "Négociation salaire"
    )
    
    print("🔥 SECTION 3: CONSEILS RECRUTEURS")
    print("=" * 80)
    
    # Test 7: Évaluation candidats
    test_conseil_rh(
        recruiter_id, 
        "recruiter",
        "Comment évaluer les compétences techniques d'un candidat ?", 
        "conseil",
        "Évaluation compétences techniques"
    )
    
    # Test 8: Questions entretien recruteur
    test_conseil_rh(
        recruiter_id, 
        "recruiter",
        "Quelles questions poser lors d'un entretien pour développeur ?", 
        "entretien",
        "Questions entretien développeur"
    )
    
    # Test 9: Processus recrutement
    test_conseil_rh(
        recruiter_id, 
        "recruiter",
        "Comment structurer un processus de recrutement efficace ?", 
        "conseil",
        "Processus recrutement"
    )
    
    print("🔥 SECTION 4: INFORMATIONS CONTRACTUELLES")
    print("=" * 80)
    
    # Test 10: CDI vs CDD candidat
    test_conseil_rh(
        candidate_id, 
        "candidate",
        "Quelle est la différence entre CDI et CDD ?", 
        "contrats",
        "CDI vs CDD (candidat)"
    )
    
    # Test 11: Période d'essai
    test_conseil_rh(
        candidate_id, 
        "candidate",
        "Comment fonctionne la période d'essai ?", 
        "contrats",
        "Période d'essai"
    )
    
    # Test 12: Choix contrat recruteur
    test_conseil_rh(
        recruiter_id, 
        "recruiter",
        "Quand proposer un CDD plutôt qu'un CDI ?", 
        "contrats",
        "Choix type contrat (recruteur)"
    )
    
    print("🔥 SECTION 5: DÉVELOPPEMENT COMPÉTENCES")
    print("=" * 80)
    
    # Test 13: Compétences 2024
    test_conseil_rh(
        candidate_id, 
        "candidate",
        "Quelles sont les compétences les plus demandées en 2024 ?", 
        "skills",
        "Compétences demandées 2024"
    )
    
    # Test 14: Formation continue
    test_conseil_rh(
        candidate_id, 
        "candidate",
        "Comment me former en continu dans le développement ?", 
        "skills",
        "Formation continue développement"
    )

if __name__ == "__main__":
    main()
