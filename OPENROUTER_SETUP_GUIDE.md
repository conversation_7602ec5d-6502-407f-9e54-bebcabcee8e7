# 🤖 Guide de Configuration OpenRouter LLM

## 🎯 **Architecture Implémentée**

Votre chatbot utilise maintenant une architecture sophistiquée avec LLM :

```
Client Request → Authentification → Analyse d'Intention (LLM) → Service Approprié → RAG Search → Génération Réponse (LLM) → Client
```

### 📋 **Flux de Traitement**

1. **Authentification** - Identification de l'utilisateur
2. **Analyse d'Intention (LLM)** - Détermine quel service appeler
3. **Service Approprié** - Exécute la logique métier
4. **RAG Search** - Recherche intelligente dans la base de données
5. **Génération Réponse (LLM)** - Crée une réponse personnalisée et engageante

## 🔧 **Configuration OpenRouter**

### 1. **Obtenir une Clé API**

1. Allez sur [OpenRouter.ai](https://openrouter.ai/)
2. Créez un compte
3. Obtenez votre clé API
4. Ajoutez des crédits à votre compte

### 2. **Configuration Environnement**

Créez/modifiez votre fichier `.env` :

```env
# Configuration OpenRouter LLM
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here
OPENROUTER_MODEL=anthropic/claude-3.5-sonnet
OPENROUTER_REFERER=http://localhost:5000
OPENROUTER_TITLE=Job Matching Chatbot

# Configuration Supabase (existante)
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
```

### 3. **Modèles Recommandés**

| Modèle | Prix | Qualité | Usage |
|--------|------|---------|-------|
| `anthropic/claude-3.5-sonnet` | $$$ | Excellent | Production |
| `anthropic/claude-3-haiku` | $ | Bon | Développement |
| `openai/gpt-4o-mini` | $$ | Très bon | Équilibré |
| `meta-llama/llama-3.1-8b-instruct` | $ | Bon | Économique |

## 🚀 **Services Implémentés**

### 1. **Analyse d'Intention avec LLM**

Le LLM analyse la requête utilisateur et détermine automatiquement quel service utiliser :

- **`recommendation_rh`** - Conseils pour améliorer le profil
- **`recommendation_jobs`** - Recommandations d'offres personnalisées
- **`recommendation_candidates`** - Recommandations de candidats (recruteurs)
- **`general_advice`** - Conseils généraux (CV, entretiens, etc.)
- **`rag_search`** - Recherche d'offres d'emploi (par défaut)

### 2. **Génération de Réponses avec LLM**

Le LLM génère des réponses :
- **Personnalisées** avec le nom de l'utilisateur
- **Structurées** avec emojis et formatage
- **Engageantes** avec appels à l'action
- **Contextuelles** basées sur les données trouvées

## 📊 **Exemples de Requêtes et Réponses**

### **Exemple 1: Recherche d'Emploi**
```
Requête: "développeur Python remote"
Intention détectée: rag_search
Réponse: Présentation structurée des offres Python remote avec détails complets
```

### **Exemple 2: Conseils RH**
```
Requête: "comment améliorer mon profil ?"
Intention détectée: recommendation_rh
Réponse: Conseils personnalisés pour améliorer le profil candidat
```

### **Exemple 3: Recommandations**
```
Requête: "recommande moi des offres"
Intention détectée: recommendation_jobs
Réponse: Offres personnalisées basées sur le profil utilisateur
```

## 🧪 **Tests et Validation**

### **Lancer les Tests**

```bash
# Test complet de l'intégration LLM
python test_openrouter_llm_integration.py

# Test du format client exact
python test_client_format.py

# Test général des APIs
python test_with_your_data.py
```

### **Vérifications**

1. **✅ Configuration** - Clé API valide
2. **✅ Analyse d'Intention** - LLM détermine le bon service
3. **✅ Génération** - Réponses personnalisées et engageantes
4. **✅ Fallback** - Fonctionne même sans LLM
5. **✅ Performance** - Réponses en < 5 secondes

## 🔄 **Mode Fallback**

Si OpenRouter n'est pas configuré ou indisponible, le système utilise automatiquement :

- **Analyse d'intention basique** avec mots-clés
- **Génération de réponses simple** mais fonctionnelle
- **Aucune interruption de service**

## 💰 **Coûts Estimés**

### **Utilisation Typique par Requête**

| Composant | Tokens | Coût (Claude-3.5-Sonnet) |
|-----------|--------|---------------------------|
| Analyse d'intention | ~300 | $0.0009 |
| Génération réponse | ~1500 | $0.0045 |
| **Total par requête** | ~1800 | **$0.0054** |

### **Estimation Mensuelle**

- **100 requêtes/jour** = ~$16/mois
- **500 requêtes/jour** = ~$81/mois
- **1000 requêtes/jour** = ~$162/mois

## 🛠 **Optimisations Possibles**

### 1. **Réduction des Coûts**
- Utiliser un modèle moins cher pour l'analyse d'intention
- Mettre en cache les réponses fréquentes
- Optimiser les prompts pour réduire les tokens

### 2. **Amélioration des Performances**
- Appels LLM en parallèle quand possible
- Timeout optimisé
- Retry logic pour la robustesse

### 3. **Qualité des Réponses**
- Fine-tuning des prompts
- Validation des réponses
- Feedback loop pour amélioration continue

## 🎯 **Prochaines Étapes**

1. **✅ Configurez votre clé OpenRouter**
2. **✅ Testez avec vos données**
3. **✅ Ajustez les prompts selon vos besoins**
4. **✅ Déployez en production**

## 🚨 **Sécurité**

- **Jamais** commiter la clé API dans le code
- Utiliser des variables d'environnement
- Monitorer l'utilisation pour éviter les abus
- Implémenter des rate limits si nécessaire

---

**🎉 Votre chatbot est maintenant équipé d'une IA avancée pour une expérience utilisateur exceptionnelle !**
