from flask import Blueprint, request, jsonify, current_app

interview_bp = Blueprint("interview", __name__)
"""*
*
*   GET
*
"""
# 🔹 GET /interviews — To<PERSON> les entretiens (option : filtre, pagination)
@interview_bp.route("/", methods=["GET"])
def get_interviews():
    supabase = current_app.supabase

    # Filtres optionnels
    candidate_id = request.args.get("candidate_id")
    recruiter_id = request.args.get("recruiter_id")
    job_id = request.args.get("job_id")
    status = request.args.get("status")

    # Pagination params facultatifs
    page = request.args.get("page", type=int, default=None)
    page_size = request.args.get("page_size", type=int, default=None)

    try:
        # Build query with filters
        query = supabase.table("interviews").select("*")

        if candidate_id:
            query = query.eq("candidate_id", candidate_id)
        if recruiter_id:
            query = query.eq("recruiter_id", recruiter_id)
        if job_id:
            query = query.eq("job_id", job_id)
        if status:
            query = query.eq("status", status)

        # Order by created_at descending
        query = query.order("created_at", desc=True)

        # Execute query
        result = query.execute()
        interviews = result.data

        # Handle pagination
        if page is not None and page_size is not None:
            if page < 1 or page_size < 1:
                return jsonify({"error": "Paramètres de pagination invalides"}), 400

            total = len(interviews)
            total_pages = (total + page_size - 1) // page_size if total > 0 else 0
            start = (page - 1) * page_size
            end = start + page_size
            paginated = interviews[start:end]

            return jsonify({
                "results": paginated,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }), 200
        else:
            return jsonify(interviews), 200

    except Exception as e:
        return jsonify({"error": f"Erreur lors de la récupération des entretiens: {str(e)}"}), 500


# 🔹 GET /interviews/<interview_id> — Récupérer un entretien spécifique
@interview_bp.route("/<interview_id>", methods=["GET"])
def get_interview_by_id(interview_id):
    supabase = current_app.supabase

    try:
        result = supabase.table("interviews").select("*").eq("id", interview_id).execute()

        if not result.data:
            return jsonify({"error": "Entretien non trouvé"}), 404

        return jsonify(result.data[0]), 200

    except Exception as e:
        return jsonify({"error": f"Erreur lors de la récupération de l'entretien: {str(e)}"}), 500


"""*
*
*   ADD
*
"""
# 🔹 Planifier (créer) un entretien
@interview_bp.route("/", methods=["POST"])
def create_interview():
    supabase = current_app.supabase
    data = request.get_json()

    required_fields = ["candidate_id", "recruiter_id", "job_id", "date", "link", "message"]
    if not all(field in data for field in required_fields):
        return jsonify({"error": "Champs requis manquants"}), 400

    try:
        # Prepare interview data
        interview_data = {
            "candidate_id": data["candidate_id"],
            "recruiter_id": data["recruiter_id"],
            "job_id": data["job_id"],
            "date": data["date"],
            "link": data["link"],
            "message": data["message"],
            "status": data.get("status", "prévu")  # Default status
        }

        # Insert into Supabase
        result = supabase.table("interviews").insert(interview_data).execute()

        if result.data:
            return jsonify(result.data[0]), 201
        else:
            return jsonify({"error": "Erreur lors de la création de l'entretien"}), 500

    except Exception as e:
        return jsonify({"error": f"Erreur lors de la création de l'entretien: {str(e)}"}), 500
"""*
*
*   UPDATE
*
"""

# 🔹 Mettre à jour un entretien (par recruiter_id/job_id/candidate_id)
@interview_bp.route("/<recruiter_id>/<job_id>/<candidate_id>", methods=["PUT"])
def update_interview(recruiter_id, job_id, candidate_id):
    supabase = current_app.supabase
    data = request.get_json()

    if not data:
        return jsonify({"error": "Aucune donnée fournie"}), 400

    try:
        # Find the interview by the triplet
        query = supabase.table("interviews").select("*").eq("recruiter_id", recruiter_id).eq("job_id", job_id).eq("candidate_id", candidate_id)
        result = query.execute()

        if not result.data:
            return jsonify({"error": "Entretien non trouvé"}), 404

        interview = result.data[0]
        interview_id = interview["id"]

        # Prepare update data
        update_data = {}
        allowed_fields = ["date", "link", "message", "status"]
        for key in allowed_fields:
            if key in data:
                update_data[key] = data[key]

        if not update_data:
            return jsonify({"error": "Aucun champ valide à mettre à jour"}), 400

        # Update the interview
        update_result = supabase.table("interviews").update(update_data).eq("id", interview_id).execute()

        if update_result.data:
            return jsonify(update_result.data[0]), 200
        else:
            return jsonify({"error": "Erreur lors de la mise à jour"}), 500

    except Exception as e:
        return jsonify({"error": f"Erreur lors de la mise à jour de l'entretien: {str(e)}"}), 500

"""*
*
*   DELETE
*
"""
# 🔹 Supprimer un entretien (par recruiter_id/job_id/candidate_id)
@interview_bp.route("/<recruiter_id>/<job_id>/<candidate_id>", methods=["DELETE"])
def delete_interview(recruiter_id, job_id, candidate_id):
    supabase = current_app.supabase

    try:
        # Find the interview by the triplet
        query = supabase.table("interviews").select("*").eq("recruiter_id", recruiter_id).eq("job_id", job_id).eq("candidate_id", candidate_id)
        result = query.execute()

        if not result.data:
            return jsonify({"error": "Entretien non trouvé"}), 404

        interview = result.data[0]
        interview_id = interview["id"]

        # Delete the interview
        delete_result = supabase.table("interviews").delete().eq("id", interview_id).execute()

        if delete_result.data:
            return jsonify({"message": "Entretien supprimé avec succès"}), 200
        else:
            return jsonify({"error": "Erreur lors de la suppression"}), 500

    except Exception as e:
        return jsonify({"error": f"Erreur lors de la suppression de l'entretien: {str(e)}"}), 500
