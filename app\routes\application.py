from flask import Blueprint, jsonify, current_app, request

application_bp = Blueprint("application", __name__)

# 🔹 Récupérer toutes les applications
@application_bp.route("/", methods=["GET"])
def get_applications():
    try:
        supabase = current_app.supabase
        result = supabase.table("applications").select("*").execute()
        return jsonify(result.data), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 🔹 Récupérer les applications par job_id
@application_bp.route('/job/<job_id>', methods=['GET'])
def get_applications_by_job(job_id):
    try:
        supabase = current_app.supabase
        result = supabase.table("applications").select("*").eq("job_id", job_id).execute()
        return jsonify(result.data), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 🔹 Mettre à jour le statut d'une application
@application_bp.route('/<application_id>', methods=['PUT'])
def update_application(application_id):
    try:
        supabase = current_app.supabase
        data = request.json

        update_data = {
            "status": data.get("status"),
            "score": data.get("score"),
            "global_score": data.get("global_score"),
            "skill_score": data.get("skill_score")
        }

        # Remove None values
        update_data = {k: v for k, v in update_data.items() if v is not None}

        response = supabase.table("applications").update(update_data).eq("id", application_id).execute()
        return jsonify({"message": "Application updated successfully", "data": response.data}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500
