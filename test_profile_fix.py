#!/usr/bin/env python3
"""
Test pour verifier que l'erreur du profile service est corrigee
"""

import requests
import json

def test_profile_endpoint():
    """Test l'endpoint /profile pour verifier qu'il n'y a plus d'erreur JSON"""
    
    url = "http://localhost:5000/profile"
    
    print("🧪 Test de l'endpoint /profile")
    print("=" * 40)
    print(f"URL: {url}")
    print("=" * 40)
    
    try:
        print("📡 Test de l'endpoint profile...")
        
        # Test sans authentification (devrait retourner 401)
        response = requests.get(url, timeout=10)
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ SUCCESS - Endpoint accessible (401 Unauthorized attendu)")
            try:
                json_response = response.json()
                print(f"📝 Reponse: {json_response}")
                return True
            except:
                print(f"📝 Reponse brute: {response.text}")
                return True
                
        elif response.status_code == 500:
            print("❌ ERREUR 500 - Le probleme JSON persiste")
            try:
                error_response = response.json()
                print(f"🚨 Erreur: {error_response}")
            except:
                print(f"🚨 Erreur brute: {response.text}")
            return False
            
        else:
            print(f"📊 Status inattendu: {response.status_code}")
            try:
                json_response = response.json()
                print(f"📝 Reponse: {json_response}")
            except:
                print(f"📝 Reponse brute: {response.text}")
            return True
            
    except requests.exceptions.ConnectionError:
        print("❌ SERVEUR NON ACCESSIBLE")
        print("💡 Demarrez le serveur avec: python run.py")
        return False
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        return False

def test_safe_json_parse():
    """Test la fonction safe_json_parse localement"""
    
    print("\n🧪 Test de la fonction safe_json_parse")
    print("=" * 40)
    
    # Importer la fonction depuis le service
    try:
        import sys
        import os
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.services.profile_service import safe_json_parse
        
        # Tests de la fonction
        test_cases = [
            (None, [], "None -> []"),
            ("[]", [], "String vide -> []"),
            ("[1,2,3]", [1,2,3], "String JSON valide -> liste"),
            ([1,2,3], [1,2,3], "Liste deja parsee -> liste"),
            ("invalid json", [], "JSON invalide -> []"),
            ({}, {}, "Dict deja parse -> dict"),
            ('{"key": "value"}', {"key": "value"}, "String JSON dict -> dict"),
            ("", [], "String vide -> []")
        ]
        
        all_passed = True
        
        for input_data, expected, description in test_cases:
            try:
                result = safe_json_parse(input_data)
                if result == expected:
                    print(f"✅ {description}")
                else:
                    print(f"❌ {description} - Attendu: {expected}, Recu: {result}")
                    all_passed = False
            except Exception as e:
                print(f"❌ {description} - Exception: {e}")
                all_passed = False
        
        if all_passed:
            print("\n🎉 Tous les tests de safe_json_parse passent!")
        else:
            print("\n⚠️ Certains tests de safe_json_parse echouent")
            
        return all_passed
        
    except ImportError as e:
        print(f"❌ Impossible d'importer safe_json_parse: {e}")
        return False

def test_server_health():
    """Test si le serveur repond"""
    try:
        response = requests.get("http://localhost:5000/", timeout=3)
        return response.status_code == 200
    except:
        return False

def main():
    """Fonction principale"""
    print("🚀 Test de correction de l'erreur profile service")
    print("Verifie que l'erreur JSON 'must be str, bytes or bytearray, not list' est corrigee")
    
    # Test de la fonction utilitaire
    json_test_passed = test_safe_json_parse()
    
    # Verifier si le serveur repond
    print("\n🔍 Verification du serveur...")
    if test_server_health():
        print("✅ Serveur accessible")
        
        # Test de l'endpoint profile
        profile_test_passed = test_profile_endpoint()
        
    else:
        print("❌ Serveur non accessible")
        print("💡 Demarrez le serveur avec: python run.py")
        profile_test_passed = False
    
    # Resultat final
    print("\n" + "=" * 40)
    print("📊 RESULTAT FINAL")
    print("=" * 40)
    
    if json_test_passed and profile_test_passed:
        print("🎉 CORRECTION REUSSIE!")
        print("✅ La fonction safe_json_parse fonctionne")
        print("✅ L'endpoint /profile ne retourne plus d'erreur 500")
        print("✅ Le probleme JSON est corrige")
    elif json_test_passed:
        print("⚠️ CORRECTION PARTIELLE")
        print("✅ La fonction safe_json_parse fonctionne")
        print("❌ Impossible de tester l'endpoint (serveur non accessible)")
    else:
        print("❌ CORRECTION INCOMPLETE")
        print("❌ Des problemes persistent")
    
    print(f"\n📋 Probleme original:")
    print("Error getting profile data: the JSON object must be str, bytes or bytearray, not list")
    print(f"\n🔧 Solution appliquee:")
    print("- Ajout de la fonction safe_json_parse()")
    print("- Gestion robuste des donnees JSON/liste")
    print("- Remplacement de tous les json.loads() problematiques")
    
    return json_test_passed and profile_test_passed

if __name__ == "__main__":
    main()
