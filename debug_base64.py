#!/usr/bin/env python3
"""
Debug du problème base64 avec le script bash
"""

import base64
import json

def test_base64_encoding():
    """Test différentes méthodes d'encodage base64"""
    
    user_data = {"id": "df82c6a7-72cd-4e06-ad09-5a4d0127bca4", "role": "candidate"}
    user_json = json.dumps(user_data)
    
    print("🔍 Debug de l'encodage base64")
    print("="*50)
    
    print(f"📝 JSON original: {user_json}")
    print(f"📏 Longueur JSON: {len(user_json)}")
    print()
    
    # Méthode Python correcte
    python_encoded = base64.b64encode(user_json.encode('utf-8')).decode('utf-8')
    print(f"🐍 Python encodé: {python_encoded}")
    print(f"📏 Longueur: {len(python_encoded)}")
    
    # Test de décodage
    try:
        decoded = base64.b64decode(python_encoded).decode('utf-8')
        decoded_data = json.loads(decoded)
        print(f"✅ Décodage Python OK: {decoded_data}")
    except Exception as e:
        print(f"❌ Erreur décodage Python: {e}")
    
    print()
    
    # Simuler le problème bash (avec retours à la ligne)
    bash_with_newlines = python_encoded + '\n'
    print(f"🐚 Bash avec \\n: '{bash_with_newlines}'")
    print(f"📏 Longueur: {len(bash_with_newlines)}")
    
    try:
        decoded_bash = base64.b64decode(bash_with_newlines).decode('utf-8')
        decoded_data_bash = json.loads(decoded_bash)
        print(f"✅ Décodage bash avec \\n OK: {decoded_data_bash}")
    except Exception as e:
        print(f"❌ Erreur décodage bash avec \\n: {e}")
    
    print()
    
    # Test avec espaces
    bash_with_spaces = python_encoded + ' '
    print(f"🐚 Bash avec espace: '{bash_with_spaces}'")
    
    try:
        decoded_space = base64.b64decode(bash_with_spaces).decode('utf-8')
        decoded_data_space = json.loads(decoded_space)
        print(f"✅ Décodage bash avec espace OK: {decoded_data_space}")
    except Exception as e:
        print(f"❌ Erreur décodage bash avec espace: {e}")
    
    print()
    print("💡 Solutions pour bash:")
    print("1. Utiliser: echo -n '$USER_JSON' | base64 | tr -d '\\n'")
    print("2. Ou: echo -n '$USER_JSON' | base64 -w 0")
    print("3. Ou utiliser Python directement")
    
    print()
    print("🧪 Commande bash corrigée:")
    print(f"USER_JSON='{user_json}'")
    print("USER_B64=$(echo -n \"$USER_JSON\" | base64 | tr -d '\\n')")
    print(f"# Résultat attendu: {python_encoded}")

def test_your_bash_output():
    """Test avec une sortie bash typique qui pourrait causer des problèmes"""
    
    print("\n" + "="*50)
    print("🔧 Test de décodage avec sortie bash problématique")
    print("="*50)
    
    # Exemples de ce que bash pourrait générer
    problematic_outputs = [
        "eyJpZCI6ICJkZjgyYzZhNy03MmNkLTRlMDYtYWQwOS01YTRkMDEyN2JjYTQiLCAicm9sZSI6ICJjYW5kaWRhdGUifQ==\n",
        "eyJpZCI6ICJkZjgyYzZhNy03MmNkLTRlMDYtYWQwOS01YTRkMDEyN2JjYTQiLCAicm9sZSI6ICJjYW5kaWRhdGUifQ== ",
        "eyJpZCI6ICJkZjgyYzZhNy03MmNkLTRlMDYtYWQwOS01YTRkMDEyN2JjYTQiLCAicm9sZSI6ICJjYW5kaWRhdGUifQ==\r\n",
    ]
    
    for i, output in enumerate(problematic_outputs, 1):
        print(f"\n🧪 Test {i}: '{repr(output)}'")
        
        try:
            # Nettoyer la sortie
            cleaned = output.strip()
            decoded = base64.b64decode(cleaned).decode('utf-8')
            data = json.loads(decoded)
            print(f"✅ Après nettoyage: {data}")
        except Exception as e:
            print(f"❌ Erreur même après nettoyage: {e}")

if __name__ == "__main__":
    test_base64_encoding()
    test_your_bash_output()
