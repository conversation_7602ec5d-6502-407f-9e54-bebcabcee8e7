#!/usr/bin/env python3
"""
Test du chat endpoint avec vos données exactes
"""

import requests
import json

def test_chat_endpoint():
    """Test l'endpoint chat avec les données exactes du client"""
    
    url = "http://localhost:5000/chat/"
    
    # Données exactes du client
    headers = {
        "accept": "application/json",
        "accept-language": "fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "content-type": "application/json",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site"
    }
    
    # Body exact du client
    body_data = {
        "query": " hi*",
        "pageContext": "",
        "role": "candidate",
        "user": {
            "id": "63800628-55a2-4c33-97ce-5bcc5ee6a1bb",
            "role": "candidate"
        }
    }
    
    print("🧪 Test avec les données exactes du client")
    print("=" * 60)
    print(f"URL: {url}")
    print(f"Headers: {json.dumps(headers, indent=2)}")
    print(f"Body: {json.dumps(body_data, indent=2)}")
    print("=" * 60)
    
    try:
        response = requests.post(
            url,
            headers=headers,
            json=body_data,
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ SUCCESS!")
            try:
                json_response = response.json()
                print(f"📝 Response: {json.dumps(json_response, indent=2, ensure_ascii=False)}")
            except:
                print(f"📝 Raw Response: {response.text}")
        else:
            print("❌ FAILED!")
            try:
                error_response = response.json()
                print(f"🚨 Error: {json.dumps(error_response, indent=2, ensure_ascii=False)}")
            except:
                print(f"🚨 Raw Error: {response.text}")
                
    except requests.exceptions.RequestException as e:
        print(f"💥 Request Exception: {e}")
    except Exception as e:
        print(f"💥 General Exception: {e}")

def test_variations():
    """Test quelques variations de la requête"""
    
    url = "http://localhost:5000/chat/"
    
    test_cases = [
        {
            "name": "Salutation simple",
            "query": "hi",
            "pageContext": "",
            "expected": "greeting"
        },
        {
            "name": "Recherche développeur",
            "query": "développeur Python",
            "pageContext": "recherche",
            "expected": "job_search"
        },
        {
            "name": "Demande de conseils",
            "query": "comment améliorer mon CV",
            "pageContext": "conseil",
            "expected": "advice"
        },
        {
            "name": "Recommandations",
            "query": "recommande moi des offres",
            "pageContext": "",
            "expected": "recommendations"
        }
    ]
    
    user_data = {
        "id": "63800628-55a2-4c33-97ce-5bcc5ee6a1bb",
        "role": "candidate"
    }
    
    headers = {
        "content-type": "application/json"
    }
    
    print("\n🔄 Test de variations")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print(f"Query: {test_case['query']}")
        print(f"Context: {test_case['pageContext']}")
        
        body_data = {
            "query": test_case['query'],
            "pageContext": test_case['pageContext'],
            "role": "candidate",
            "user": user_data
        }
        
        try:
            response = requests.post(url, headers=headers, json=body_data, timeout=15)
            
            if response.status_code == 200:
                print("✅ SUCCESS")
                json_response = response.json()
                response_text = json_response.get('response', '')
                print(f"📝 Response: {response_text[:150]}...")
            else:
                print(f"❌ FAILED - Status: {response.status_code}")
                try:
                    error = response.json()
                    print(f"🚨 Error: {error}")
                except:
                    print(f"🚨 Raw Error: {response.text}")
                    
        except Exception as e:
            print(f"💥 Exception: {e}")
        
        print("-" * 40)

def main():
    """Fonction principale"""
    print("🚀 Test du Chat Endpoint")
    print("Teste l'endpoint /chat/ avec vos données exactes")
    
    # Test principal avec données exactes
    test_chat_endpoint()
    
    # Tests de variations
    test_variations()
    
    print("\n✅ Tests terminés!")

if __name__ == "__main__":
    main()
