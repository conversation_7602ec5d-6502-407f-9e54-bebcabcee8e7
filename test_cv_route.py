#!/usr/bin/env python3
"""
Test script to verify the CV file serving route fix
"""
import requests
import time

def test_cv_route():
    """Test the new CV file serving route"""
    print("🧪 Testing CV file serving route fix...")
    
    # Wait a moment for server to start
    time.sleep(2)
    
    base_url = "http://localhost:5000"
    
    # Test the problematic URL pattern from the error log
    test_file_path = "C:/Users/<USER>/Desktop/backend/backendV1/app/uploads/cvs/1879125b-271e-4e2a-b7fb-0058ccce4e59_cv.pdf"
    test_url = f"{base_url}/cv/file/{test_file_path}"
    
    try:
        print(f"📡 Testing URL: {test_url}")
        response = requests.get(test_url, timeout=5)
        
        if response.status_code == 200:
            print("✅ SUCCESS: CV file served successfully!")
            print(f"   Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            print(f"   Content-Length: {len(response.content)} bytes")
            return True
        else:
            print(f"❌ FAILED: HTTP {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ FAILED: Could not connect to server")
        print("💡 Make sure the server is running with: python run.py")
        return False
    except Exception as e:
        print(f"❌ FAILED: {str(e)}")
        return False

def test_alternative_routes():
    """Test alternative CV serving routes"""
    print("\n🔍 Testing alternative routes...")
    
    base_url = "http://localhost:5000"
    filename = "1879125b-271e-4e2a-b7fb-0058ccce4e59_cv.pdf"
    
    # Test the download route
    download_url = f"{base_url}/cv/download/{filename}"
    print(f"📡 Testing download route: {download_url}")
    
    try:
        response = requests.get(download_url, timeout=5)
        if response.status_code == 200:
            print("✅ Download route works!")
        else:
            print(f"❌ Download route failed: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Download route error: {str(e)}")
    
    # Test the static file route
    static_url = f"{base_url}/uploads/cvs/{filename}"
    print(f"📡 Testing static route: {static_url}")
    
    try:
        response = requests.get(static_url, timeout=5)
        if response.status_code == 200:
            print("✅ Static route works!")
        else:
            print(f"❌ Static route failed: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Static route error: {str(e)}")

if __name__ == "__main__":
    print("🚀 CV Route Fix Test")
    print("=" * 50)
    
    success = test_cv_route()
    test_alternative_routes()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 CV file serving route fix is working!")
    else:
        print("⚠️  CV file serving route needs more work")
    print("=" * 50)
