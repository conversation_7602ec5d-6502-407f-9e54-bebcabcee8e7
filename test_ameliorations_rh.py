#!/usr/bin/env python3
"""
Test des améliorations RH
"""

import requests
import json
import base64

def test_conseil_ameliore(user_id, role, query, page_context, description):
    """Test un conseil amélioré"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": user_id, "role": role}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"🔧 {description}")
    print(f"👤 {role.upper()} | 💬 {query}")
    print("-" * 70)
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            response_text = result['response']
            
            print(f"✅ SUCCÈS!")
            print(f"📏 Longueur: {len(response_text)} caractères")
            
            # Analyser la qualité
            if "**" in response_text and len(response_text) > 500:
                print(f"🌟 Type: Conseil spécialisé détaillé")
            elif any(keyword in response_text for keyword in ["conseil", "guide", "étapes"]):
                print(f"💡 Type: Conseil structuré")
            else:
                print(f"📝 Type: Réponse standard")
            
            # Compter les sections
            sections = response_text.count('**')
            if sections > 5:
                print(f"📋 Sections structurées: {sections//2}")
            
            print(f"\n📖 EXTRAIT (200 premiers caractères):")
            print("=" * 70)
            print(response_text[:200] + "..." if len(response_text) > 200 else response_text)
            print("=" * 70)
            
        else:
            error = response.json()
            print(f"❌ ERREUR: {error}")
            
    except Exception as e:
        print(f"💥 EXCEPTION: {e}")
    
    print("\n" + "🔚" * 35 + "\n")

def main():
    print("🚀 TEST DES AMÉLIORATIONS RH")
    print("=" * 70)
    
    # IDs des utilisateurs
    recruiter_id = "e714bb4b-b3f8-4343-8d5d-1e120e421b29"  # John Doe
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"  # Mohamed ouabbi
    
    print("🔥 SECTION 1: CONSEILS RECRUTEURS AMÉLIORÉS")
    print("=" * 70)
    
    # Test 1: Évaluation compétences (amélioré)
    test_conseil_ameliore(
        recruiter_id, 
        "recruiter",
        "Comment évaluer les compétences techniques d'un candidat ?", 
        "conseil",
        "Évaluation compétences (AMÉLIORÉ)"
    )
    
    # Test 2: Questions entretien (amélioré)
    test_conseil_ameliore(
        recruiter_id, 
        "recruiter",
        "Quelles questions poser lors d'un entretien développeur ?", 
        "entretien",
        "Questions entretien (AMÉLIORÉ)"
    )
    
    # Test 3: Choix contrat (amélioré)
    test_conseil_ameliore(
        recruiter_id, 
        "recruiter",
        "Quand proposer un CDD plutôt qu'un CDI ?", 
        "contrats",
        "Choix contrat (AMÉLIORÉ)"
    )
    
    print("🔥 SECTION 2: CONSEILS CANDIDATS AMÉLIORÉS")
    print("=" * 70)
    
    # Test 4: CV amélioré
    test_conseil_ameliore(
        candidate_id, 
        "candidate",
        "Comment améliorer mon CV pour développeur ?", 
        "conseil",
        "Amélioration CV (AMÉLIORÉ)"
    )
    
    # Test 5: Négociation salaire (amélioré)
    test_conseil_ameliore(
        candidate_id, 
        "candidate",
        "Comment négocier mon salaire ?", 
        "conseil",
        "Négociation salaire (AMÉLIORÉ)"
    )
    
    print("🔥 SECTION 3: GESTION RATE LIMITS")
    print("=" * 70)
    
    # Test 6: Forcer plusieurs appels pour tester rate limit
    for i in range(3):
        test_conseil_ameliore(
            candidate_id, 
            "candidate",
            f"Compétences demandées 2024 test {i+1}", 
            "skills",
            f"Test rate limit {i+1}/3"
        )

if __name__ == "__main__":
    main()
