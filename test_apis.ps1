# PowerShell script to test your APIs with the provided data
# Make sure your Flask server is running on localhost:5000

$BaseUrl = "http://localhost:5000"
$Headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "🚀 Testing APIs with PowerShell" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

function Test-API {
    param(
        [string]$Method,
        [string]$Endpoint,
        [hashtable]$Params = @{},
        [string]$Description
    )
    
    $Url = "$BaseUrl$Endpoint"
    
    # Add query parameters if provided
    if ($Params.Count -gt 0) {
        $QueryString = ($Params.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
        $Url += "?$QueryString"
    }
    
    Write-Host "`n$Description" -ForegroundColor Cyan
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        $Response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers -TimeoutSec 10
        Write-Host "✅ SUCCESS" -ForegroundColor Green
        
        if ($Response -is [array]) {
            Write-Host "📊 Found $($Response.Count) items" -ForegroundColor Yellow
        } elseif ($Response.jobs) {
            Write-Host "📊 Found $($Response.jobs.Count) jobs" -ForegroundColor Yellow
            if ($Response.jobs.Count -gt 0) {
                Write-Host "📝 First job: $($Response.jobs[0].title)" -ForegroundColor Yellow
            }
        } elseif ($Response.PSObject.Properties.Count -gt 0) {
            Write-Host "📝 Response keys: $($Response.PSObject.Properties.Name -join ', ')" -ForegroundColor Yellow
        }
        
        return $true
    }
    catch {
        Write-Host "❌ FAILED" -ForegroundColor Red
        Write-Host "🚨 Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test counters
$Passed = 0
$Failed = 0

# Test 1: Get all jobs
if (Test-API -Method "GET" -Endpoint "/job" -Description "📋 1. Get all jobs") {
    $Passed++
} else {
    $Failed++
}

# Test 2: Get specific jobs by ID
$JobIds = @(
    @{Id="11111111-aaaa-bbbb-cccc-111111111111"; Title="Data Scientist Junior"},
    @{Id="22222222-bbbb-cccc-dddd-222222222222"; Title="OCR Developer"},
    @{Id="2b7dbbf0-10ee-4d05-b588-640a0762e3e3"; Title="Senior Full Stack"},
    @{Id="44444444-4444-4444-4444-444444444444"; Title="Senior Frontend"},
    @{Id="66666666-6666-6666-6666-666666666666"; Title="Senior Backend"}
)

$Counter = 2
foreach ($Job in $JobIds) {
    if (Test-API -Method "GET" -Endpoint "/job/$($Job.Id)" -Description "🔍 $Counter. Get $($Job.Title)") {
        $Passed++
    } else {
        $Failed++
    }
    $Counter++
}

# Test 3: Search jobs with specific skills
$SearchTerms = @("Python", "React", "Docker", "TensorFlow")
foreach ($Term in $SearchTerms) {
    if (Test-API -Method "GET" -Endpoint "/job" -Params @{search=$Term; limit=5} -Description "🔍 $Counter. Search jobs with '$Term'") {
        $Passed++
    } else {
        $Failed++
    }
    $Counter++
}

# Test 4: Filter by locations
$Locations = @("Remote", "Casablanca", "Paris")
foreach ($Location in $Locations) {
    if (Test-API -Method "GET" -Endpoint "/job" -Params @{location=$Location; limit=5} -Description "🌍 $Counter. Filter by location '$Location'") {
        $Passed++
    } else {
        $Failed++
    }
    $Counter++
}

# Test 5: Filter by contract types
$ContractTypes = @("CDI", "CDD", "Freelance")
foreach ($Contract in $ContractTypes) {
    if (Test-API -Method "GET" -Endpoint "/job" -Params @{contract_type=$Contract; limit=5} -Description "📄 $Counter. Filter by contract '$Contract'") {
        $Passed++
    } else {
        $Failed++
    }
    $Counter++
}

# Test 6: Filter by salary
$SalaryRanges = @(8000, 10000, 50000, 70000)
foreach ($Salary in $SalaryRanges) {
    if (Test-API -Method "GET" -Endpoint "/job" -Params @{min_salary=$Salary; limit=5} -Description "💰 $Counter. Filter by min salary $Salary") {
        $Passed++
    } else {
        $Failed++
    }
    $Counter++
}

# Test 7: Test pagination
if (Test-API -Method "GET" -Endpoint "/job" -Params @{page=1; limit=3} -Description "📄 $Counter. Test pagination (page 1)") {
    $Passed++
} else {
    $Failed++
}
$Counter++

if (Test-API -Method "GET" -Endpoint "/job" -Params @{page=2; limit=3} -Description "📄 $Counter. Test pagination (page 2)") {
    $Passed++
} else {
    $Failed++
}
$Counter++

# Test 8: Get recommended jobs
if (Test-API -Method "GET" -Endpoint "/job/recommended" -Description "🎯 $Counter. Get recommended jobs") {
    $Passed++
} else {
    $Failed++
}
$Counter++

# Test 9: Get all candidates
if (Test-API -Method "GET" -Endpoint "/candidates/" -Description "👥 $Counter. Get all candidates") {
    $Passed++
} else {
    $Failed++
}
$Counter++

# Test 10: Combined filters
if (Test-API -Method "GET" -Endpoint "/job" -Params @{search="developer"; location="Remote"; contract_type="CDI"; limit=5} -Description "🔧 $Counter. Combined filters test") {
    $Passed++
} else {
    $Failed++
}

# Summary
Write-Host "`n============================================" -ForegroundColor Green
Write-Host "📊 TEST SUMMARY" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green
Write-Host "✅ Tests Passed: $Passed" -ForegroundColor Green
Write-Host "❌ Tests Failed: $Failed" -ForegroundColor Red
$SuccessRate = [math]::Round(($Passed / ($Passed + $Failed)) * 100, 1)
Write-Host "📈 Success Rate: $SuccessRate%" -ForegroundColor Yellow

if ($Failed -eq 0) {
    Write-Host "`n🎉 All tests passed! Your APIs are working correctly." -ForegroundColor Green
} else {
    Write-Host "`n⚠️ $Failed test(s) failed. Please check the server logs." -ForegroundColor Yellow
}

Write-Host "`n📋 Your data should include:" -ForegroundColor Cyan
Write-Host "• Data Scientist Junior (Casablanca)" -ForegroundColor White
Write-Host "• OCR Developer (Rabat)" -ForegroundColor White
Write-Host "• Senior Full Stack Engineer (Casablanca)" -ForegroundColor White
Write-Host "• Senior Frontend Developer (Paris, Remote)" -ForegroundColor White
Write-Host "• Senior Backend Engineer (Remote)" -ForegroundColor White
