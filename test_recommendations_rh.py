#!/usr/bin/env python3
"""
Test spécifique des recommandations et conseils RH
"""

import requests
import json
import base64

def test_query(user_id, role, query, page_context, description):
    """Test une query spécifique"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": user_id, "role": role}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"🎯 {description}")
    print(f"👤 User: {role}")
    print(f"💬 Query: {query}")
    print(f"📋 Context: {page_context}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès!")
            response_text = result['response']
            
            # Analyser le type de réponse
            if "**1." in response_text:
                count = response_text.count("**") // 2
                print(f"📈 Nombre de recommandations: {count}")
            
            if "Compatibilité:" in response_text:
                print(f"🎯 Type: Recommandation avec scores")
            elif any(keyword in response_text for keyword in ["conseil", "recommande", "suggère"]):
                print(f"💡 Type: Conseil RH")
            elif any(keyword in response_text for keyword in ["CDI", "CDD", "contrat"]):
                print(f"📄 Type: Information contractuelle")
            
            # Extraire le nom d'utilisateur
            if "Bonjour" in response_text:
                greeting_line = response_text.split('\n')[0]
                print(f"👋 Salutation: {greeting_line}")
            
            if len(response_text) > 400:
                print(f"📝 Réponse: {response_text[:400]}...")
                print(f"[Réponse complète: {len(response_text)} caractères]")
            else:
                print(f"📝 Réponse: {response_text}")
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")
    
    print("\n" + "="*80 + "\n")

def main():
    print("🎯 Test des recommandations et conseils RH")
    print("="*80)
    
    # IDs des utilisateurs réels
    recruiter_id = "e714bb4b-b3f8-4343-8d5d-1e120e421b29"  # John Doe
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"  # Mohamed ouabbi
    
    print("🔥 SECTION 1: RECOMMANDATIONS CANDIDATS")
    print("="*80)
    
    # Test 1: Recommandations générales
    test_query(
        candidate_id, 
        "candidate",
        "Peux-tu me recommander des offres d'emploi ?", 
        "recherche_emploi",
        "Test 1: Recommandations générales candidat"
    )
    
    # Test 2: Recommandations avec compétences
    test_query(
        candidate_id, 
        "candidate",
        "Quelles opportunités correspondent à mes compétences en développement ?", 
        "recherche_emploi",
        "Test 2: Recommandations basées compétences"
    )
    
    # Test 3: Matching profil
    test_query(
        candidate_id, 
        "candidate",
        "Trouve-moi des emplois en matching avec mon profil", 
        "recherche_emploi",
        "Test 3: Matching profil candidat"
    )
    
    print("🔥 SECTION 2: RECOMMANDATIONS RECRUTEURS")
    print("="*80)
    
    # Test 4: Recommandations candidats avec job ID
    test_query(
        recruiter_id, 
        "recruiter",
        "Recommande-moi des candidats pour l'offre 11111111-aaaa-bbbb-cccc-111111111111", 
        "recherche_emploi",
        "Test 4: Recommandations candidats Data Scientist"
    )
    
    # Test 5: Candidats pour poste technique
    test_query(
        recruiter_id, 
        "recruiter",
        "Trouve des profils pour l'offre 22222222-bbbb-cccc-dddd-222222222222", 
        "recherche_emploi",
        "Test 5: Candidats OCR & Computer Vision"
    )
    
    print("🔥 SECTION 3: CONSEILS RH CANDIDATS")
    print("="*80)
    
    # Test 6: Conseil CV
    test_query(
        candidate_id, 
        "candidate",
        "Comment améliorer mon CV pour un poste de développeur ?", 
        "conseil",
        "Test 6: Conseil amélioration CV"
    )
    
    # Test 7: Préparation entretien
    test_query(
        candidate_id, 
        "candidate",
        "Comment me préparer pour un entretien technique ?", 
        "entretien",
        "Test 7: Préparation entretien technique"
    )
    
    # Test 8: Négociation salaire
    test_query(
        candidate_id, 
        "candidate",
        "Comment négocier mon salaire lors d'un entretien ?", 
        "conseil",
        "Test 8: Négociation salaire"
    )
    
    print("🔥 SECTION 4: CONSEILS RH RECRUTEURS")
    print("="*80)
    
    # Test 9: Évaluation candidats
    test_query(
        recruiter_id, 
        "recruiter",
        "Comment évaluer les compétences techniques d'un candidat ?", 
        "conseil",
        "Test 9: Évaluation compétences techniques"
    )
    
    # Test 10: Questions entretien
    test_query(
        recruiter_id, 
        "recruiter",
        "Quelles questions poser lors d'un entretien pour un développeur ?", 
        "entretien",
        "Test 10: Questions entretien développeur"
    )
    
    print("🔥 SECTION 5: INFORMATIONS CONTRACTUELLES")
    print("="*80)
    
    # Test 11: CDI vs CDD candidat
    test_query(
        candidate_id, 
        "candidate",
        "Quelle est la différence entre CDI et CDD ?", 
        "contrats",
        "Test 11: CDI vs CDD (candidat)"
    )
    
    # Test 12: Choix contrat recruteur
    test_query(
        recruiter_id, 
        "recruiter",
        "Quand proposer un CDD plutôt qu'un CDI ?", 
        "contrats",
        "Test 12: Choix type contrat (recruteur)"
    )
    
    print("🔥 SECTION 6: CONSEILS COMPÉTENCES")
    print("="*80)
    
    # Test 13: Compétences demandées
    test_query(
        candidate_id, 
        "candidate",
        "Quelles sont les compétences les plus demandées en 2024 ?", 
        "skills",
        "Test 13: Compétences demandées 2024"
    )
    
    # Test 14: Formation recommandée
    test_query(
        candidate_id, 
        "candidate",
        "Quelle formation suivre pour devenir data scientist ?", 
        "skills",
        "Test 14: Formation data scientist"
    )

if __name__ == "__main__":
    main()
