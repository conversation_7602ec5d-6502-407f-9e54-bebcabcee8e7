#!/usr/bin/env python3
"""
Test rapide de validation du chatbot
"""

import requests
import json
import base64

def test_query(user_id, role, query, page_context, description):
    """Test une query spécifique"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": user_id, "role": role}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"🧪 {description}")
    print(f"👤 {role} | 💬 {query}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=45)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            response_text = result['response']
            
            # Analyser le type de réponse
            if "**1." in response_text:
                count = response_text.count("**") // 2
                print(f"✅ {count} recommandations trouvées")
            elif "Bonjour" in response_text and ("conseil" in response_text.lower() or "recommande" in response_text.lower()):
                print(f"✅ Conseil personnalisé fourni")
            elif any(keyword in response_text for keyword in ["CDI", "CDD", "contrat"]):
                print(f"✅ Information contractuelle")
            else:
                print(f"✅ Réponse générée")
            
            # Afficher un extrait
            if len(response_text) > 150:
                print(f"📝 {response_text[:150]}...")
            else:
                print(f"📝 {response_text}")
        else:
            error = response.json()
            print(f"❌ Erreur: {error}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")
    
    print("-" * 60)

def main():
    print("🚀 Test rapide de validation du chatbot")
    print("=" * 60)
    
    # IDs des utilisateurs
    recruiter_id = "e714bb4b-b3f8-4343-8d5d-1e120e421b29"  # John Doe
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"  # Mohamed ouabbi
    
    # Test 1: Candidat - Recommandations
    test_query(
        candidate_id, 
        "candidate",
        "Recommande-moi des offres", 
        "recherche_emploi",
        "Recommandations candidat"
    )
    
    # Test 2: Recruteur - Recommandations avec job ID
    test_query(
        recruiter_id, 
        "recruiter",
        "Candidats pour l'offre 11111111-aaaa-bbbb-cccc-111111111111", 
        "recherche_emploi",
        "Recommandations recruteur"
    )
    
    # Test 3: Candidat - Recherche normale
    test_query(
        candidate_id, 
        "candidate",
        "Data Scientist", 
        "recherche_emploi",
        "Recherche Data Scientist"
    )
    
    # Test 4: Candidat - Conseil CV
    test_query(
        candidate_id, 
        "candidate",
        "Comment améliorer mon CV ?", 
        "conseil",
        "Conseil CV"
    )
    
    # Test 5: Candidat - Info contrats
    test_query(
        candidate_id, 
        "candidate",
        "Différence CDI CDD ?", 
        "contrats",
        "Info contrats"
    )
    
    # Test 6: Recruteur - Recherche normale
    test_query(
        recruiter_id, 
        "recruiter",
        "Frontend React", 
        "recherche_emploi",
        "Recherche Frontend"
    )
    
    # Test 7: Candidat - Préparation entretien
    test_query(
        candidate_id, 
        "candidate",
        "Préparer entretien technique", 
        "entretien",
        "Préparation entretien"
    )
    
    # Test 8: Candidat - Compétences
    test_query(
        candidate_id, 
        "candidate",
        "Compétences demandées 2024", 
        "skills",
        "Compétences 2024"
    )

if __name__ == "__main__":
    main()
