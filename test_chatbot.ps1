# Script PowerShell pour tester le chatbot avec header X-User base64

$URL = "http://127.0.0.1:5000/chat/"

# Utilise un ID valide de ta base de données
$USER_JSON = '{"id":"4db01413-00e5-4c1c-90f7-4c03438d1dc3","role":"candidate"}'

# Encoder en base64 (PowerShell)
$USER_BYTES = [System.Text.Encoding]::UTF8.GetBytes($USER_JSON)
$USER_B64 = [System.Convert]::ToBase64String($USER_BYTES)

Write-Host "🔍 Informations de debug:" -ForegroundColor Cyan
Write-Host "USER_JSON: $USER_JSON"
Write-Host "USER_B64: $USER_B64"
Write-Host "Longueur USER_B64: $($USER_B64.Length)"
Write-Host ""

# Test de validation de l'encodage
Write-Host "🧪 Test de validation de l'encodage:" -ForegroundColor Yellow
try {
    $DECODED_BYTES = [System.Convert]::FromBase64String($USER_B64)
    $DECODED_JSON = [System.Text.Encoding]::UTF8.GetString($DECODED_BYTES)
    $DECODED_DATA = $DECODED_JSON | ConvertFrom-Json
    Write-Host "✅ Encodage valide: $DECODED_JSON" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur encodage: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Questions de test
$QUERIES = @(
    '{"query": "Comment améliorer mon CV pour un poste de data scientist ?", "pageContext": "conseil"}',
    '{"query": "Que dois-je mettre dans une lettre de motivation pour une startup tech ?", "pageContext": "lettre_motivation"}',
    '{"query": "Quelle est la différence entre un CDI et un CDD ?", "pageContext": "contrats"}',
    '{"query": "Comment me préparer à un entretien développeur backend ?", "pageContext": "entretien"}',
    '{"query": "Développeur Python", "pageContext": "recherche_emploi"}',
    '{"query": "Frontend React", "pageContext": "recherche_emploi"}'
)

Write-Host "🚀 Début des tests du chatbot" -ForegroundColor Green
Write-Host "============================================"

for ($i = 0; $i -lt $QUERIES.Length; $i++) {
    $query = $QUERIES[$i]
    $testNum = $i + 1
    $total = $QUERIES.Length
    
    Write-Host ""
    Write-Host "🤖 Test $testNum/$total:" -ForegroundColor Cyan
    Write-Host "📝 Question: $query"
    Write-Host ""
    
    try {
        # Faire la requête avec Invoke-RestMethod
        $headers = @{
            "Content-Type" = "application/json"
            "X-User" = $USER_B64
        }
        
        $response = Invoke-RestMethod -Uri $URL -Method POST -Headers $headers -Body $query -ErrorAction Stop
        
        Write-Host "📊 Réponse du serveur:" -ForegroundColor Green
        if ($response.response) {
            Write-Host "✅ Succès!" -ForegroundColor Green
            $responseText = $response.response
            if ($responseText.Length -gt 150) {
                Write-Host "💬 Réponse: $($responseText.Substring(0, 150))..." -ForegroundColor White
                Write-Host "   [Réponse complète: $($responseText.Length) caractères]" -ForegroundColor Gray
            } else {
                Write-Host "💬 Réponse: $responseText" -ForegroundColor White
            }
        } elseif ($response.error) {
            Write-Host "❌ Erreur: $($response.error)" -ForegroundColor Red
        } else {
            Write-Host "📄 Réponse: $($response | ConvertTo-Json)" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "💥 Erreur requête: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "   Status Code: $statusCode" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "============================================"
}

Write-Host ""
Write-Host "🎉 Tests terminés!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 Notes:" -ForegroundColor Yellow
Write-Host "- Si tu vois des erreurs 404 'Candidate not found', remplace l'ID dans USER_JSON"
Write-Host "- Les réponses devraient commencer par 'Bonjour [nom]!' maintenant"
