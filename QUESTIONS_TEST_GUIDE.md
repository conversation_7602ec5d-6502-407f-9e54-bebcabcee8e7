# 🧪 Guide de Questions Variées pour Tester votre Chatbot

## 🎯 **Objectif**

Tester votre chatbot avec différents types de questions pour valider :
- **Analyse d'intention** avec LLM OpenRouter
- **Génération de réponses** personnalisées
- **Recherche RAG** dans votre base de données
- **Adaptation selon le rôle** (candidat vs recruteur)

## 🚀 **Comment Tester**

### **Option 1: Script Python Automatisé (Recommandé)**
```bash
python questions_test_variees.py
```

### **Option 2: Script Bash avec Curl**
```bash
bash curl_questions_variees.sh
```

### **Option 3: Tests Manuels**
Utilisez les questions ci-dessous avec votre format curl exact.

## 📋 **Catégories de Questions**

### 1. **🤝 SALUTATIONS**
*Intention attendue: `general_advice`*

- "Bonjour !"
- "Salut, comment ça va ?"
- "Hello"
- "Hi there"
- "Bonsoir"
- "<PERSON><PERSON><PERSON> !"

**Réponse attendue :** Salutation personnalisée avec "Bonjour Taoufiq !" et présentation des services.

### 2. **💼 RECHERCHE EMPLOI GÉNÉRALE**
*Intention attendue: `rag_search`*

- "Je cherche un travail"
- "Quels sont les postes disponibles ?"
- "Montrez-moi les offres d'emploi"
- "Y a-t-il des opportunités ?"
- "Je veux voir les jobs"
- "Donnez-moi la liste des emplois"

**Réponse attendue :** Liste structurée des offres avec emojis et détails.

### 3. **🔧 RECHERCHE PAR COMPÉTENCES**
*Intention attendue: `rag_search`*

- "Je cherche un poste en Python"
- "Avez-vous des offres pour développeur React ?"
- "Jobs en intelligence artificielle"
- "Postes data scientist"
- "Emploi développeur full stack"
- "Offres en machine learning"
- "Travail avec TensorFlow"
- "Poste développeur backend Node.js"

**Réponse attendue :** Offres filtrées par compétences avec mise en avant des technologies.

### 4. **📍 RECHERCHE PAR LOCALISATION**
*Intention attendue: `rag_search`*

- "Jobs à Casablanca"
- "Emplois à Rabat"
- "Postes en remote"
- "Travail à distance"
- "Offres à Paris"
- "Jobs hybrides"
- "Emploi sur site à Marrakech"

**Réponse attendue :** Offres filtrées par localisation avec détails géographiques.

### 5. **💰 RECHERCHE PAR SALAIRE**
*Intention attendue: `rag_search`*

- "Postes avec salaire supérieur à 50000"
- "Jobs bien payés"
- "Emplois avec bon salaire"
- "Offres plus de 15000 MAD"
- "Postes 70000 euros minimum"

**Réponse attendue :** Offres avec fourchettes salariales mises en avant.

### 6. **📄 RECHERCHE PAR CONTRAT**
*Intention attendue: `rag_search`*

- "Contrats CDI disponibles"
- "Jobs en CDD"
- "Missions freelance"
- "Stages disponibles"
- "Contrats temps plein"

**Réponse attendue :** Offres filtrées par type de contrat.

### 7. **🎯 RECOMMANDATIONS PERSONNALISÉES**
*Intention attendue: `recommendation_jobs`*

- "Recommandez-moi des offres"
- "Quels postes me correspondent ?"
- "Suggérez-moi des emplois"
- "Offres adaptées à mon profil"
- "Jobs qui matchent avec moi"
- "Proposez-moi des opportunités"

**Réponse attendue :** Recommandations basées sur le profil utilisateur avec explication du matching.

### 8. **📈 CONSEILS RH**
*Intention attendue: `recommendation_rh`*

- "Comment améliorer mon CV ?"
- "Conseils pour mon profil"
- "Comment augmenter mes chances ?"
- "Que faire pour être plus attractif ?"
- "Améliorer mes compétences"
- "Développer mon profil professionnel"
- "Comment me démarquer ?"

**Réponse attendue :** Conseils personnalisés pour améliorer le profil candidat.

### 9. **🎤 CONSEILS ENTRETIEN**
*Intention attendue: `general_advice`*

- "Comment préparer un entretien ?"
- "Conseils entretien technique"
- "Questions fréquentes en entretien"
- "Comment réussir mon entretien ?"
- "Préparation entretien vidéo"
- "Que dire en entretien ?"
- "Comment négocier le salaire ?"

**Réponse attendue :** Conseils pratiques pour la préparation d'entretiens.

### 10. **📊 QUESTIONS SECTEUR**
*Intention attendue: `general_advice`*

- "Tendances du marché IT"
- "Secteurs qui recrutent"
- "Compétences demandées en 2024"
- "Évolution du marché emploi"
- "Technologies populaires"
- "Métiers d'avenir"

**Réponse attendue :** Informations sur les tendances du marché de l'emploi.

### 11. **🧠 QUESTIONS COMPLEXES**
*Intention attendue: Variable selon le contexte*

- "Je suis développeur Python avec 5 ans d'expérience, je cherche un poste remote en IA avec salaire 80k+"
- "Reconversion vers la data science, quelles compétences acquérir ?"
- "Freelance vs CDI dans le développement web, que choisir ?"
- "Comment passer de junior à senior developer ?"
- "Négociation salaire pour poste senior backend"
- "Changer de ville pour un meilleur poste, conseils ?"

**Réponse attendue :** Analyse nuancée avec conseils personnalisés et offres pertinentes.

### 12. **👔 QUESTIONS RECRUTEUR**
*Intention attendue: `recommendation_candidates`*

- "Comment trouver de bons candidats ?"
- "Profils Python disponibles"
- "Candidats pour poste senior"
- "Comment évaluer un développeur ?"
- "Sourcing de talents IT"
- "Candidats avec expérience React"

**Réponse attendue :** Conseils de recrutement et présentation de candidats.

## 🔍 **Indicateurs de Qualité à Vérifier**

### **✅ Réponse de Qualité :**
- **Salutation personnalisée** : "Bonjour Taoufiq !" ou "Bonjour Mohammed Amine !"
- **Emojis structurants** : 🏢, 📍, 💰, 🔧, 📝, ✅, 🎯, 💡
- **Format organisé** : Utilisation de **gras** et listes
- **Données réelles** : Offres de votre base de données
- **Appel à l'action** : Encouragement à poser d'autres questions
- **Longueur appropriée** : 200-1500 caractères selon le contexte

### **🎯 Analyse d'Intention Correcte :**
- **Salutations** → `general_advice`
- **Recherche emploi** → `rag_search`
- **Recommandations** → `recommendation_jobs`
- **Conseils RH** → `recommendation_rh`
- **Questions recruteur** → `recommendation_candidates`

## 📊 **Exemple de Test Complet**

```bash
# Test d'une question complexe
curl 'http://localhost:5000/chat/' \
  -H 'Content-Type: application/json' \
  --data-raw '{"query":"Je cherche un poste Python remote avec salaire 60k+","pageContext":"","role":"candidate","user":{"id":"63800628-55a2-4c33-97ce-5bcc5ee6a1bb","role":"candidate"}}'
```

**Réponse attendue :**
```json
{
  "response": "Bonjour Taoufiq ! Voici les offres Python en remote qui correspondent à vos critères :\n\n**1. Senior Backend Engineer**\n🏢 Entreprise: [Nom]\n📍 Lieu: Remote\n💰 Salaire: 70000 - 90000 EUR\n🔧 Compétences: Python, Node.js, AWS, PostgreSQL\n\n**2. Senior Software Engineer (Full Stack)**\n🏢 Entreprise: [Nom]\n📍 Lieu: Remote\n💰 Salaire: 10000 - 15000 MAD\n🔧 Compétences: Python, Django, React.js\n\n💡 Ces postes offrent d'excellentes opportunités en remote avec Python. N'hésitez pas à me demander des conseils pour optimiser votre candidature !"
}
```

## 🚀 **Lancer les Tests**

1. **Démarrez le serveur :**
   ```bash
   python run.py
   ```

2. **Lancez les tests automatisés :**
   ```bash
   python questions_test_variees.py
   ```

3. **Analysez les résultats :**
   - Taux de réussite global
   - Qualité des réponses par catégorie
   - Pertinence de l'analyse d'intention

**🎉 Votre chatbot sera testé avec plus de 50 questions variées pour valider toutes les fonctionnalités !**
