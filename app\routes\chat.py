from flask import Blueprint, request, jsonify, current_app, make_response
import requests
import os
import json
import re
import base64
from app.services.matching_service import get_matching_service
from app.services.llm_service import get_openrouter_llm_service
from typing import Dict, List

rag_bp = Blueprint("chat", __name__)

def authenticate_user(data, request_obj):
    """
    Authentifie l'utilisateur via header X-User (base64) ou objet user dans le body JSON
    Retourne: (user_id, user_role, user_data)
    """
    try:
        # Méthode 1: Header X-User (base64)
        x_user_header = request_obj.headers.get('X-User')
        if x_user_header:
            try:
                # Décoder le base64
                user_json = base64.b64decode(x_user_header).decode('utf-8')
                user_data = json.loads(user_json)
                user_id = user_data.get('id')
                user_role = user_data.get('role')

                if user_id and user_role:
                    print(f"[AUTH] Authentification via header X-User: {user_id} ({user_role})")
                    return user_id, user_role, user_data
            except Exception as e:
                print(f"[AUTH] Erreur décodage header X-User: {e}")

        # Méthode 2: Objet user dans le body JSON
        user_data = data.get('user')
        if user_data:
            user_id = user_data.get('id')
            user_role = user_data.get('role')

            if user_id and user_role:
                print(f"[AUTH] Authentification via body JSON: {user_id} ({user_role})")
                return user_id, user_role, user_data

        print("[AUTH] Aucune méthode d'authentification valide trouvée")
        return None, None, None

    except Exception as e:
        print(f"[AUTH] Erreur authentification: {e}")
        return None, None, None

def get_user_details(user_id, user_role):
    """
    Récupère les détails de l'utilisateur depuis la base de données
    Retourne: (display_name, error)
    """
    try:
        supabase = current_app.supabase

        if user_role == "candidate":
            table_name = "candidates"
        elif user_role == "recruiter":
            table_name = "recruiters"
        else:
            return None, f"Rôle utilisateur invalide: {user_role}"

        # Récupérer les détails de l'utilisateur
        user_response = supabase.table(table_name).select("id, email, full_name").eq("id", user_id).execute()

        if not user_response.data:
            return None, f"Utilisateur {user_role} non trouvé avec l'ID: {user_id}"

        user_info = user_response.data[0]

        # Déterminer le nom d'affichage
        display_name = user_info.get('full_name')
        if not display_name or display_name.strip() == "":
            # Fallback sur l'email si pas de nom complet
            email = user_info.get('email', '')
            if email:
                display_name = email.split('@')[0]  # Prendre la partie avant @
            else:
                display_name = f"Utilisateur {user_id[:8]}"

        print(f"[USER] Détails récupérés: {display_name} ({user_role})")
        return display_name, None

    except Exception as e:
        print(f"[USER] Erreur récupération détails: {e}")
        return None, f"Erreur lors de la récupération des détails utilisateur: {str(e)}"

def generate_specialized_advice(query, page_context, user_role, display_name):
    """Génère des conseils spécialisés selon le rôle et le contexte"""

    greeting = f"Bonjour {display_name} !"
    query_lower = query.lower()

    # Conseils spécialisés pour recruteurs
    if user_role == "recruiter":
        if page_context == "conseil" or any(keyword in query_lower for keyword in ["évaluer", "compétences", "candidat", "recrutement"]):
            return f"""{greeting}

**Conseils pour évaluer les compétences techniques :**

**1. Tests techniques pratiques :**
- Exercices de code en temps réel
- Résolution de problèmes concrets
- Revue de code existant

**2. Questions comportementales :**
- "Décrivez un projet technique complexe que vous avez mené"
- "Comment gérez-vous les deadlines serrés ?"
- "Parlez-moi d'un échec et de ce que vous en avez appris"

**3. Évaluation des soft skills :**
- Capacité de communication technique
- Travail en équipe et collaboration
- Adaptabilité et apprentissage continu

**4. Vérification des références :**
- Contactez les anciens employeurs
- Demandez des exemples de projets
- Vérifiez les certifications

💡 Adaptez votre évaluation selon le niveau du poste (junior/senior)."""

        elif page_context == "entretien" or any(keyword in query_lower for keyword in ["questions", "entretien", "interview"]):
            return f"""{greeting}

**Questions essentielles pour un entretien développeur :**

**Questions techniques :**
- "Expliquez la différence entre [concept A] et [concept B]"
- "Comment optimiseriez-vous cette requête/algorithme ?"
- "Décrivez votre processus de debugging"

**Questions sur l'expérience :**
- "Quel est le projet le plus complexe que vous ayez réalisé ?"
- "Comment restez-vous à jour avec les nouvelles technologies ?"
- "Décrivez votre environnement de développement idéal"

**Questions sur la collaboration :**
- "Comment gérez-vous les conflits dans une équipe ?"
- "Décrivez votre expérience avec les méthodologies agiles"
- "Comment expliquez-vous des concepts techniques à des non-techniques ?"

**Questions sur la motivation :**
- "Pourquoi voulez-vous rejoindre notre équipe ?"
- "Où vous voyez-vous dans 3 ans ?"
- "Qu'est-ce qui vous passionne dans le développement ?"

💡 Laissez toujours du temps au candidat pour poser ses questions !"""

        elif page_context == "contrats":
            return f"""{greeting}

**Guide pour choisir le type de contrat :**

**Proposer un CDD quand :**
- Remplacement temporaire d'un salarié absent
- Surcroît d'activité ponctuel (projet spécifique)
- Emploi saisonnier ou cyclique
- Test avant embauche définitive (avec accord candidat)

**Proposer un CDI quand :**
- Besoin permanent et durable
- Poste stratégique pour l'entreprise
- Volonté de fidéliser le talent
- Développement à long terme

**Considérations légales :**
- CDD limité à 18 mois maximum (renouvelable une fois)
- Justification obligatoire pour le CDD
- Indemnité de fin de contrat (10% du salaire brut)
- Période d'essai plus courte en CDD

💡 Le CDI reste la norme et rassure les candidats de qualité."""

    # Conseils spécialisés pour candidats
    elif user_role == "candidate":
        if page_context == "conseil" and any(keyword in query_lower for keyword in ["cv", "curriculum", "candidature"]):
            return f"""{greeting}

**Guide complet pour améliorer votre CV :**

**Structure et contenu :**
- **En-tête** : Nom, titre du poste visé, contact
- **Résumé professionnel** : 2-3 lignes sur votre profil
- **Compétences techniques** : Langages, frameworks, outils
- **Expérience** : Projets concrets avec résultats mesurables
- **Formation** : Diplômes et certifications pertinentes

**Conseils de rédaction :**
- Utilisez des verbes d'action (développé, optimisé, dirigé)
- Quantifiez vos réalisations (amélioration de 30%, équipe de 5 personnes)
- Adaptez le CV à chaque offre
- Maximum 2 pages pour un profil expérimenté

**Erreurs à éviter :**
- Fautes d'orthographe et de grammaire
- CV générique non adapté
- Manque de mots-clés du secteur
- Informations personnelles non pertinentes

💡 Créez un portfolio en ligne pour compléter votre CV !"""

        elif page_context == "conseil" and any(keyword in query_lower for keyword in ["salaire", "négocier", "rémunération"]):
            return f"""{greeting}

**Guide de négociation salariale :**

**Préparation :**
- Recherchez les salaires du marché pour votre poste
- Listez vos réalisations et valeur ajoutée
- Préparez une fourchette (minimum acceptable - objectif)
- Considérez l'ensemble du package (avantages, formation)

**Timing optimal :**
- Après une offre ferme, pas avant
- Lors de l'évaluation annuelle
- Après un succès majeur ou nouvelle responsabilité

**Techniques de négociation :**
- Commencez par remercier pour l'offre
- Présentez vos arguments factuels
- Restez professionnel et positif
- Soyez prêt à négocier d'autres avantages

**Alternatives au salaire :**
- Jours de congés supplémentaires
- Formation et certifications
- Télétravail ou flexibilité horaire
- Bonus de performance

💡 La négociation montre votre valeur, pas votre cupidité !"""

    return generate_fallback_response(query, page_context, user_role, display_name)

def generate_fallback_response(query, page_context, user_role, display_name):
    """Génère une réponse fallback intelligente quand aucune offre n'est trouvée"""

    greeting = f"Bonjour {display_name} !"

    # Réponses contextuelles selon le pageContext
    context_responses = {
        "contrats": f"""
        {greeting}

        Excellente question sur les types de contrats ! Voici les principales différences :

        **CDI (Contrat à Durée Indéterminée) :**
        - Contrat sans date de fin prévue
        - Plus de stabilité et sécurité d'emploi
        - Période d'essai variable selon le poste
        - Préavis requis pour démissionner ou licencier

        **CDD (Contrat à Durée Déterminée) :**
        - Contrat avec date de fin fixée à l'avance
        - Utilisé pour remplacements, surcroît d'activité, projets temporaires
        - Durée maximale généralement de 18 mois (renouvelable une fois)
        - Indemnité de fin de contrat (10% du salaire brut)

        Le CDI reste la norme en France et offre plus de protection sociale.
        """,

        "conseil": f"""
        {greeting}

        Je n'ai pas trouvé d'offres spécifiques pour "{query}", mais voici mes conseils généraux :

        **Pour optimiser votre recherche :**
        - Utilisez des mots-clés plus spécifiques liés à votre domaine
        - Consultez régulièrement les nouvelles offres
        - Adaptez votre CV aux compétences demandées

        **Conseils carrière :**
        - Développez vos compétences techniques
        - Créez un portfolio en ligne
        - Participez à des projets open source
        - Restez à jour avec les tendances de votre secteur
        """,

        "skills": f"""
        {greeting}

        Concernant "{query}", voici mon analyse :

        **Compétences techniques en demande :**
        - Langages de programmation modernes (Python, JavaScript, Java)
        - Technologies cloud (AWS, Azure, Docker)
        - Frameworks populaires selon votre domaine
        - Outils de versioning (Git) et CI/CD

        **Conseils d'apprentissage :**
        - Commencez par les fondamentaux
        - Pratiquez sur des projets concrets
        - Suivez des formations certifiantes
        - Rejoignez des communautés de développeurs
        """,

        "entretien": f"""
        {greeting}

        Excellente initiative de vous préparer ! Voici mes conseils pour réussir vos entretiens :

        **Préparation technique :**
        - Révisez les concepts fondamentaux de votre domaine
        - Préparez des exemples concrets de vos réalisations
        - Entraînez-vous aux exercices de code si applicable

        **Préparation comportementale :**
        - Préparez des réponses aux questions classiques
        - Documentez-vous sur l'entreprise et ses valeurs
        - Préparez des questions pertinentes à poser

        **Le jour J :**
        - Arrivez en avance
        - Soyez authentique et montrez votre motivation
        - Écoutez attentivement et posez des questions
        """
    }

    # Réponse par défaut si le contexte n'est pas reconnu
    default_response = f"""
    {greeting}

    Je n'ai pas trouvé d'offres d'emploi correspondant exactement à "{query}" dans notre base de données actuelle.

    **Suggestions :**
    - Essayez avec des mots-clés différents ou plus généraux
    - Vérifiez l'orthographe de votre recherche
    - Consultez régulièrement car de nouvelles offres sont ajoutées

    **Je peux vous aider avec :**
    - Conseils pour améliorer votre CV
    - Préparation aux entretiens
    - Informations sur les types de contrats
    - Conseils sur les compétences à développer

    N'hésitez pas à me poser une question plus spécifique !
    """

    return context_responses.get(page_context, default_response).strip()
  
def get_job_recommendations_for_candidate(candidate_id, display_name, limit=3):
    """Récupère les recommandations d'offres pour un candidat"""
    try:
        print(f"[INFO] Recherche de recommandations pour candidat {candidate_id}")

        # D'abord, vérifier s'il y a des matches existants dans la table
        supabase = current_app.supabase
        existing_matches = supabase.table("candidate_job_matches") \
            .select("job_id, match_score, match_percentage, matched_skills") \
            .eq("candidate_id", candidate_id) \
            .order("match_score", desc=True) \
            .limit(limit).execute()

        if existing_matches.data:
            print(f"[INFO] Trouvé {len(existing_matches.data)} matches existants")
            # Récupérer les détails des jobs
            job_ids = [match['job_id'] for match in existing_matches.data]
            jobs_response = supabase.table("jobs") \
                .select("id, title, description, location, company_id") \
                .in_("id", job_ids).execute()

            jobs_dict = {job['id']: job for job in jobs_response.data}

            # Récupérer les infos des entreprises
            company_ids = [job.get('company_id') for job in jobs_response.data if job.get('company_id')]
            companies_dict = {}
            if company_ids:
                companies_response = supabase.table("companies") \
                    .select("id, name, logo_url") \
                    .in_("id", company_ids).execute()
                companies_dict = {comp['id']: comp for comp in companies_response.data}

            # Construire la réponse
            response = f"Bonjour {display_name} ! Voici les offres d'emploi qui correspondent le mieux à votre profil :\n\n"

            for i, match in enumerate(existing_matches.data, 1):
                job = jobs_dict.get(match['job_id'], {})
                company = companies_dict.get(job.get('company_id'), {})

                response += f"**{i}. {job.get('title', 'Poste')}**\n"
                response += f"📍 Lieu: {job.get('location', 'Non spécifié')}\n"
                response += f"🏢 Entreprise: {company.get('name', 'Non spécifiée')}\n"
                response += f"📊 Compatibilité: {match.get('match_percentage', 0)}%\n"

                if match.get('matched_skills'):
                    skills_str = ', '.join(match['matched_skills'][:3])
                    response += f"✅ Compétences correspondantes: {skills_str}\n"

                response += f"📝 Description: {job.get('description', '')[:100]}...\n\n"

            response += "💡 Ces recommandations sont basées sur l'analyse de votre profil et de vos compétences. N'hésitez pas à postuler aux offres qui vous intéressent !"
            return response

        # Si pas de matches existants, calculer de nouveaux matches (plus rapide)
        print(f"[INFO] Aucun match existant, calcul de nouveaux matches...")
        matching_service = get_matching_service()
        matches = matching_service.match_candidate_to_jobs(candidate_id, limit=limit)

        if not matches:
            return f"Bonjour {display_name} ! Je n'ai pas trouvé d'offres correspondant parfaitement à votre profil pour le moment. Je vous encourage à consulter régulièrement notre plateforme car de nouvelles opportunités sont ajoutées fréquemment."

        response = f"Bonjour {display_name} ! Voici les offres d'emploi qui correspondent le mieux à votre profil :\n\n"

        for i, match in enumerate(matches, 1):
            match_percentage = match.get('match_percentage', 0)
            response += f"**{i}. {match.get('job_title', 'Poste')}**\n"
            response += f"📍 Lieu: {match.get('job_location', 'Non spécifié')}\n"
            response += f"🏢 Entreprise: {match.get('company_name', 'Non spécifiée')}\n"
            response += f"📊 Compatibilité: {match_percentage}%\n"

            if match.get('matched_skills'):
                skills_str = ', '.join(match['matched_skills'][:3])
                response += f"✅ Compétences correspondantes: {skills_str}\n"

            response += f"📝 Description: {match.get('job_description', '')[:100]}...\n\n"

        response += "💡 Ces recommandations sont basées sur l'analyse de votre profil et de vos compétences. N'hésitez pas à postuler aux offres qui vous intéressent !"

        return response

    except Exception as e:
        print(f"[ERROR] Erreur recommandations candidat: {e}")
        return f"Bonjour {display_name} ! Je rencontre une difficulté technique pour analyser votre profil. Veuillez réessayer plus tard."

def get_candidate_recommendations_for_job(job_id, display_name, limit=5):
    """Récupère les recommandations de candidats pour une offre"""
    try:
        matching_service = get_matching_service()
        matches = matching_service.match_job_to_candidates(job_id, limit=limit)

        if not matches:
            return f"Bonjour {display_name} ! Je n'ai pas trouvé de candidats correspondant parfaitement à cette offre pour le moment. Je vous encourage à élargir vos critères de recherche."

        response = f"Bonjour {display_name} ! Voici les candidats qui correspondent le mieux à votre offre :\n\n"

        for i, match in enumerate(matches, 1):
            match_percentage = match.get('match_percentage', 0)
            response += f"**{i}. {match.get('candidate_name', 'Candidat')}**\n"
            response += f"📧 Email: {match.get('candidate_email', 'Non spécifié')}\n"
            response += f"📊 Compatibilité: {match_percentage}%\n"

            if match.get('matched_skills'):
                skills_str = ', '.join(match['matched_skills'][:3])
                response += f"✅ Compétences correspondantes: {skills_str}\n"

            if match.get('candidate_skills'):
                all_skills = ', '.join(match['candidate_skills'][:5])
                response += f"🔧 Compétences principales: {all_skills}\n"

            response += "\n"

        response += "💡 Ces recommandations sont basées sur l'analyse des profils et compétences des candidats. N'hésitez pas à les contacter pour plus d'informations !"

        return response

    except Exception as e:
        print(f"[ERROR] Erreur recommandations recruteur: {e}")
        return f"Bonjour {display_name} ! Je rencontre une difficulté technique pour analyser les candidats. Veuillez réessayer plus tard."

def detect_recommendation_intent(query, page_context, user_role):
    """Détecte si l'utilisateur demande des recommandations"""
    query_lower = query.lower()

    # Mots-clés pour recommandations candidat
    candidate_keywords = [
        'recommande', 'recommandation', 'suggère', 'suggestion', 'propose', 'proposition',
        'offres pour moi', 'emplois pour moi', 'postes pour moi', 'opportunités',
        'matching', 'correspondant', 'adapté', 'convient'
    ]

    # Mots-clés pour recommandations recruteur
    recruiter_keywords = [
        'candidats pour', 'profils pour', 'recommande candidat', 'suggère candidat',
        'qui correspond', 'matching candidat', 'trouve candidat'
    ]

    if user_role == "candidate":
        return any(keyword in query_lower for keyword in candidate_keywords)
    elif user_role == "recruiter":
        return any(keyword in query_lower for keyword in recruiter_keywords)

    return False

def detect_rh_recommendation_intent(query):
    """Détecte si l'utilisateur demande des recommandations RH pour améliorer son profil"""
    query_lower = query.lower()

    # Mots-clés spécifiques pour les recommandations d'amélioration de profil
    rh_keywords = [
        'améliorer', 'amélioration', 'conseil', 'conseils', 'comment',
        'augmenter', 'score', 'matching', 'profil', 'compétences',
        'développer', 'manque', 'manquant', 'lacune', 'gap',
        'formation', 'apprendre', 'skill', 'skills', 'recommandation rh',
        'aide', 'aider', 'progresser', 'évoluer', 'carrière'
    ]

    # Phrases typiques
    rh_phrases = [
        'comment améliorer', 'que dois-je', 'quoi apprendre', 'quelles compétences',
        'conseil pour', 'aide moi', 'recommandation pour', 'comment augmenter'
    ]

    # Vérifier les mots-clés
    keyword_match = any(keyword in query_lower for keyword in rh_keywords)

    # Vérifier les phrases
    phrase_match = any(phrase in query_lower for phrase in rh_phrases)

    return keyword_match or phrase_match

def calculate_candidate_job_scores(candidate_id: str, job_ids: List[str] = None, limit: int = 20) -> List[Dict]:
    """
    Calcule les scores réels entre un candidat et des offres d'emploi en utilisant le service de matching

    Args:
        candidate_id (str): UUID du candidat
        job_ids (List[str], optional): Liste spécifique d'IDs de jobs à analyser
        limit (int): Nombre maximum de jobs à analyser

    Returns:
        List[Dict]: Liste des jobs avec leurs scores de matching réels
    """
    try:
        print(f"[INFO] Calcul des scores réels pour candidat {candidate_id}")

        # Utiliser le service de matching existant
        matching_service = get_matching_service()
        matches = matching_service.match_candidate_to_jobs(candidate_id, job_ids, limit)

        if not matches:
            print("[WARNING] Aucun match trouvé avec le service de matching")
            return []

        # Transformer les résultats pour notre analyse
        scored_jobs = []
        for match in matches:
            scored_job = {
                'job_id': match.get('job_id'),
                'job_title': match.get('job_title', ''),
                'job_location': match.get('job_location', ''),
                'job_skills': match.get('job_skills', []),
                'candidate_skills': match.get('candidate_skills', []),
                'matched_skills': match.get('matched_skills', []),
                'match_score': match.get('match_score', 0),
                'match_percentage': match.get('match_percentage', 0),
                'sbert_similarity': match.get('sbert_similarity', 0),
                'skill2vec_similarity': match.get('skill2vec_similarity', 0),
                'prediction': match.get('prediction', 'no_match')
            }
            scored_jobs.append(scored_job)

        print(f"[INFO] Calculé {len(scored_jobs)} scores de matching")
        return scored_jobs

    except Exception as e:
        print(f"[ERROR] Erreur dans calculate_candidate_job_scores: {e}")
        return []

def recommendation_candidate_rh(candidate_id: str, display_name: str) -> str:
    """
    Analyse le profil d'un candidat et les offres d'emploi pour donner des recommandations
    d'amélioration du score de matching entre job et profile_candidate

    Args:
        candidate_id (str): UUID du candidat
        display_name (str): Nom d'affichage du candidat

    Returns:
        str: Recommandations personnalisées pour améliorer le matching
    """
    try:
        print(f"[INFO] Génération de recommandations RH pour candidat {candidate_id}")

        supabase = current_app.supabase

        # 1. Récupérer le profil complet du candidat
        candidate_profile = supabase.table("candidate_profiles") \
            .select("skillner_skills, py_skills, added_skills, skills, experience, education, cv, location") \
            .eq("candidate_id", candidate_id) \
            .execute()

        if not candidate_profile.data:
            return f"Bonjour {display_name} ! Je n'ai pas pu accéder à votre profil. Veuillez vous assurer que votre profil est complet."

        profile = candidate_profile.data[0]

        # 2. Combiner toutes les compétences du candidat
        candidate_skills = set()

        # Récupérer les compétences de toutes les sources
        skill_sources = ['skillner_skills', 'py_skills', 'added_skills', 'skills']
        for source in skill_sources:
            skills_list = profile.get(source)
            if skills_list and isinstance(skills_list, list):
                candidate_skills.update([skill.strip().lower() for skill in skills_list if skill and skill.strip()])

        candidate_skills = list(candidate_skills)

        # 3. Calculer les scores réels avec le service de matching
        scored_jobs = calculate_candidate_job_scores(candidate_id, limit=20)

        if not scored_jobs:
            return f"Bonjour {display_name} ! Aucune offre d'emploi n'est actuellement disponible pour l'analyse ou votre profil n'est pas assez complet."

        # 4. Analyser les compétences et scores
        all_job_skills = set()
        job_skills_frequency = {}
        location_frequency = {}
        job_scores = {}  # Nouveau: stocker les scores par job

        # Récupérer les détails des jobs pour l'analyse géographique
        job_ids = [job['job_id'] for job in scored_jobs if job.get('job_id')]
        jobs_details = supabase.table("jobs") \
            .select("id, location") \
            .in_("id", job_ids) \
            .execute()

        job_locations = {job['id']: job.get('location', '') for job in jobs_details.data}

        for scored_job in scored_jobs:
            job_id = scored_job.get('job_id')

            # Analyser les compétences du job
            job_skills = scored_job.get('job_skills', []) or []

            for skill in job_skills:
                if skill and isinstance(skill, str):
                    skill_clean = skill.strip().lower()
                    if skill_clean:
                        all_job_skills.add(skill_clean)
                        job_skills_frequency[skill_clean] = job_skills_frequency.get(skill_clean, 0) + 1

            # Analyser les localisations
            location = job_locations.get(job_id, '').strip()
            if location:
                location_frequency[location] = location_frequency.get(location, 0) + 1

            # Stocker le score du job
            if job_id:
                job_scores[job_id] = {
                    'match_score': scored_job.get('match_score', 0),
                    'match_percentage': scored_job.get('match_percentage', 0),
                    'matched_skills': scored_job.get('matched_skills', []),
                    'title': scored_job.get('job_title', '')
                }

        # 5. Identifier les compétences manquantes
        candidate_skills_set = set(candidate_skills)
        missing_skills = all_job_skills - candidate_skills_set

        # 6. Trier les compétences manquantes par fréquence (les plus demandées)
        missing_skills_ranked = sorted(
            missing_skills,
            key=lambda x: job_skills_frequency.get(x, 0),
            reverse=True
        )[:10]  # Top 10 compétences manquantes

        # 7. Identifier les compétences que le candidat possède déjà
        matching_skills = candidate_skills_set & all_job_skills
        matching_skills_ranked = sorted(
            matching_skills,
            key=lambda x: job_skills_frequency.get(x, 0),
            reverse=True
        )[:5]  # Top 5 compétences correspondantes

        # 8. Analyser la localisation
        candidate_location = profile.get('location', '').strip()
        location_recommendations = []
        if candidate_location:
            # Vérifier si la localisation du candidat correspond aux offres
            matching_locations = [loc for loc in location_frequency.keys()
                                if candidate_location.lower() in loc.lower() or loc.lower() in candidate_location.lower()]
            if not matching_locations:
                top_locations = sorted(location_frequency.items(), key=lambda x: x[1], reverse=True)[:3]
                location_recommendations = [loc[0] for loc in top_locations]

        # 9. Analyser les performances actuelles
        current_scores = [job_scores[job_id]['match_percentage'] for job_id in job_scores.keys()]
        avg_current_score = sum(current_scores) / len(current_scores) if current_scores else 0
        best_matches = sorted(job_scores.items(), key=lambda x: x[1]['match_percentage'], reverse=True)[:3]

        # 10. Générer les recommandations personnalisées
        response = f"Bonjour {display_name} ! 🎯\n\n"
        response += "**📊 ANALYSE DE VOTRE PROFIL ET RECOMMANDATIONS**\n\n"

        # Score actuel moyen
        response += f"**📈 VOTRE SCORE ACTUEL MOYEN : {avg_current_score:.1f}%**\n\n"

        # Meilleures correspondances actuelles
        if best_matches:
            response += "**🏆 VOS MEILLEURES CORRESPONDANCES ACTUELLES :**\n"
            for i, (job_id, job_data) in enumerate(best_matches, 1):
                response += f"{i}. {job_data['title']} - {job_data['match_percentage']:.1f}%\n"
            response += "\n"

        # Compétences actuelles
        if matching_skills_ranked:
            response += "**✅ VOS POINTS FORTS :**\n"
            for i, skill in enumerate(matching_skills_ranked, 1):
                frequency = job_skills_frequency.get(skill, 0)
                response += f"{i}. {skill.title()} (demandée dans {frequency} offres)\n"
            response += "\n"

        # Compétences à développer
        if missing_skills_ranked:
            response += "**🚀 COMPÉTENCES À DÉVELOPPER PRIORITAIREMENT :**\n"
            for i, skill in enumerate(missing_skills_ranked[:5], 1):
                frequency = job_skills_frequency.get(skill, 0)
                response += f"{i}. {skill.title()} (demandée dans {frequency} offres)\n"
            response += "\n"

            response += "**💡 CONSEILS D'APPRENTISSAGE :**\n"
            response += "- Commencez par les 2-3 premières compétences listées\n"
            response += "- Suivez des formations en ligne (Coursera, Udemy, OpenClassrooms)\n"
            response += "- Pratiquez sur des projets personnels\n"
            response += "- Obtenez des certifications reconnues\n\n"

        # Recommandations de localisation
        if location_recommendations:
            response += "**📍 OPPORTUNITÉS GÉOGRAPHIQUES :**\n"
            response += f"Votre localisation actuelle : {candidate_location}\n"
            response += "Localisations avec le plus d'opportunités :\n"
            for i, location in enumerate(location_recommendations, 1):
                count = location_frequency.get(location, 0)
                response += f"{i}. {location} ({count} offres)\n"
            response += "\n"

        # Calcul du score potentiel basé sur les vrais scores de matching
        total_skills_in_market = len(all_job_skills)
        current_matching_rate = len(matching_skills) / total_skills_in_market * 100 if total_skills_in_market > 0 else 0

        # Estimation de l'amélioration potentielle basée sur les compétences manquantes prioritaires
        high_impact_skills = missing_skills_ranked[:3]  # Top 3 des compétences manquantes
        potential_score_boost = len(high_impact_skills) * 5  # Estimation: +5% par compétence clé

        # Analyser la distribution des scores actuels
        low_scores = [score for score in current_scores if score < 50]
        medium_scores = [score for score in current_scores if 50 <= score < 75]
        high_scores = [score for score in current_scores if score >= 75]

        response += "**📈 IMPACT POTENTIEL :**\n"
        response += f"- Score moyen actuel : {avg_current_score:.1f}%\n"
        response += f"- Amélioration estimée avec top 3 compétences : +{potential_score_boost}%\n"
        response += f"- Répartition actuelle : {len(high_scores)} offres >75%, {len(medium_scores)} offres 50-75%, {len(low_scores)} offres <50%\n"
        response += f"- Nombre total de compétences analysées : {total_skills_in_market}\n\n"

        # Recommandations personnalisées basées sur les scores
        response += "**🎯 RECOMMANDATIONS PERSONNALISÉES :**\n"

        if avg_current_score < 40:
            response += "**🔴 PRIORITÉ HAUTE - Profil à renforcer significativement :**\n"
            response += "- Concentrez-vous sur les 2-3 premières compétences listées\n"
            response += "- Complétez votre profil avec plus de détails sur votre expérience\n"
            response += "- Ajoutez des projets concrets démontrant vos compétences\n"
        elif avg_current_score < 65:
            response += "**🟡 PRIORITÉ MOYENNE - Profil en développement :**\n"
            response += "- Développez les compétences manquantes prioritaires\n"
            response += "- Optimisez la description de vos expériences\n"
            response += "- Postulez aux offres avec >60% de correspondance\n"
        else:
            response += "**🟢 PROFIL SOLIDE - Optimisation fine :**\n"
            response += "- Affinez vos compétences spécialisées\n"
            response += "- Postulez aux meilleures offres (>70% de correspondance)\n"
            response += "- Considérez des postes plus senior\n"

        response += "\n**📋 PLAN D'ACTION :**\n"
        response += "1. Mettez à jour votre profil avec les nouvelles compétences acquises\n"
        response += "2. Ajoutez des projets démontrant ces compétences\n"
        response += "3. Consultez régulièrement les nouvelles offres\n"
        response += "4. Postulez aux offres correspondant à vos compétences actuelles\n\n"

        response += f"💪 Ces recommandations sont basées sur l'analyse IA de {len(scored_jobs)} offres d'emploi avec scores réels de matching. Bonne chance dans votre recherche !"

        return response

    except Exception as e:
        print(f"[ERROR] Erreur dans recommendation_candidate_rh: {e}")
        return f"Bonjour {display_name} ! Je rencontre une difficulté technique pour analyser votre profil et générer des recommandations. Veuillez réessayer plus tard."

def extract_job_id_from_query(query):
    """Extrait un ID de job de la query (format UUID)"""
    import re
    uuid_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
    match = re.search(uuid_pattern, query, re.IGNORECASE)
    return match.group(0) if match else None

def analyze_user_intent_with_llm(query, page_context, user_role):
    """
    Analyse l'intention de l'utilisateur avec OpenRouter LLM
    """
    try:
        print(f"[LLM] Analyse d'intention pour: '{query}' (contexte: '{page_context}', rôle: '{user_role}')")

        # Utiliser le service OpenRouter LLM
        llm_service = get_openrouter_llm_service()
        intent_analysis = llm_service.analyze_user_intent(query, page_context, user_role)

        print(f"[LLM] Résultat: {intent_analysis['service']} (confiance: {intent_analysis['confidence']})")
        print(f"[LLM] Raisonnement: {intent_analysis['reasoning']}")

        return intent_analysis

    except Exception as e:
        print(f"[LLM ERROR] Erreur analyse intention: {e}")
        # Fallback vers analyse basique
        return _fallback_intent_analysis(query, page_context, user_role)

def _fallback_intent_analysis(query, page_context, user_role):
    """
    Analyse d'intention de fallback sans LLM
    """
    query_lower = query.lower()

    # Détection des recommandations RH
    if detect_rh_recommendation_intent(query):
        return {
            "service": "recommendation_rh",
            "confidence": 0.8,
            "reasoning": "Détection de mots-clés pour recommandations RH",
            "extracted_data": {"keywords": ["améliorer", "conseil"]}
        }

    # Détection des recommandations d'offres
    if detect_recommendation_intent(query, page_context, user_role):
        if user_role == "candidate":
            return {
                "service": "recommendation_jobs",
                "confidence": 0.8,
                "reasoning": "Demande de recommandations d'offres pour candidat",
                "extracted_data": {"keywords": ["recommande", "offres"]}
            }
        elif user_role == "recruiter":
            return {
                "service": "recommendation_candidates",
                "confidence": 0.8,
                "reasoning": "Demande de recommandations de candidats pour recruteur",
                "extracted_data": {"keywords": ["candidats", "profils"]}
            }

    # Détection de conseils généraux
    advice_keywords = ['conseil', 'aide', 'comment', 'entretien', 'cv', 'salaire', 'contrat']
    if any(keyword in query_lower for keyword in advice_keywords):
        return {
            "service": "general_advice",
            "confidence": 0.7,
            "reasoning": "Demande de conseils généraux",
            "extracted_data": {"keywords": [k for k in advice_keywords if k in query_lower]}
        }

    # Par défaut: recherche RAG
    return {
        "service": "rag_search",
        "confidence": 0.6,
        "reasoning": "Recherche d'offres d'emploi par défaut",
        "extracted_data": {"keywords": query_lower.split()[:3]}
    }

def perform_intelligent_rag_search(query, intent_analysis):
    """
    Effectue une recherche intelligente dans la base de données des offres
    """
    try:
        supabase = current_app.supabase

        # Recherche de base dans les offres
        query_lower = query.lower()

        # Construire la requête de recherche
        jobs_query = supabase.table("jobs").select(
            "id, title, description, location, skills, requirements, company_id, contract_type, work_mode, salary_min, salary_max, salary_currency"
        )

        # Filtrer les offres actives
        jobs_query = jobs_query.eq("is_active", True)

        # Recherche par mots-clés dans le titre et la description
        search_terms = query_lower.split()
        jobs_response = jobs_query.execute()

        if not jobs_response.data:
            return []

        # Filtrer les résultats par pertinence
        relevant_jobs = []
        for job in jobs_response.data:
            job_text = f"{job.get('title', '')} {job.get('description', '')}".lower()
            job_skills = job.get('skills', []) or []
            job_requirements = job.get('requirements', []) or []

            # Calculer un score de pertinence simple
            score = 0
            for term in search_terms:
                if term in job_text:
                    score += 2
                if any(term in str(skill).lower() for skill in job_skills):
                    score += 3
                if any(term in str(req).lower() for req in job_requirements):
                    score += 1

            if score > 0:
                job['relevance_score'] = score
                relevant_jobs.append(job)

        # Trier par pertinence et limiter les résultats
        relevant_jobs.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        return relevant_jobs[:5]  # Retourner les 5 meilleures correspondances

    except Exception as e:
        print(f"[ERROR] Erreur recherche RAG: {e}")
        return []

def generate_llm_response_with_rag(query, jobs, user_role, display_name, intent_analysis):
    """
    Génère une réponse finale avec OpenRouter LLM en utilisant les données RAG
    """
    try:
        print(f"[LLM] Génération de réponse finale pour {len(jobs)} offres")

        if not jobs:
            return generate_specialized_advice(query, "", user_role, display_name)

        # Enrichir les données des offres avec les informations des entreprises
        enriched_jobs = _enrich_jobs_with_company_data(jobs)

        # Utiliser le service OpenRouter LLM pour générer la réponse
        llm_service = get_openrouter_llm_service()
        final_response = llm_service.generate_final_response(
            query, enriched_jobs, user_role, display_name, intent_analysis
        )

        if final_response:
            print(f"[LLM] Réponse générée: {len(final_response)} caractères")
            return final_response
        else:
            # Fallback vers génération simple
            print("[LLM] Fallback vers génération simple")
            return _generate_simple_fallback_response(query, enriched_jobs, display_name)

    except Exception as e:
        print(f"[LLM ERROR] Erreur génération réponse: {e}")
        return _generate_simple_fallback_response(query, jobs, display_name)

def _enrich_jobs_with_company_data(jobs):
    """
    Enrichit les données des offres avec les informations des entreprises
    """
    try:
        supabase = current_app.supabase
        company_ids = [job.get('company_id') for job in jobs if job.get('company_id')]
        companies_dict = {}

        if company_ids:
            companies_response = supabase.table("companies").select("id, name, logo_url").in_("id", company_ids).execute()
            companies_dict = {comp['id']: comp for comp in companies_response.data}

        # Enrichir chaque offre
        enriched_jobs = []
        for job in jobs:
            enriched_job = job.copy()
            company_id = job.get('company_id')
            if company_id and company_id in companies_dict:
                company = companies_dict[company_id]
                enriched_job['company_name'] = company.get('name', 'Non spécifiée')
                enriched_job['company_logo'] = company.get('logo_url', '')
            else:
                enriched_job['company_name'] = 'Non spécifiée'
                enriched_job['company_logo'] = ''

            enriched_jobs.append(enriched_job)

        return enriched_jobs

    except Exception as e:
        print(f"[ERROR] Erreur enrichissement données: {e}")
        return jobs

def _generate_simple_fallback_response(query, jobs, display_name):
    """
    Génère une réponse simple de fallback sans LLM
    """
    response = f"Bonjour {display_name} ! Voici les offres d'emploi que j'ai trouvées pour \"{query}\" :\n\n"

    for i, job in enumerate(jobs[:3], 1):  # Limiter à 3 offres
        response += f"**{i}. {job.get('title', 'Poste')}**\n"
        response += f"🏢 Entreprise: {job.get('company_name', 'Non spécifiée')}\n"
        response += f"📍 Lieu: {job.get('location', 'Non spécifié')}\n"

        if job.get('contract_type'):
            response += f"📄 Contrat: {job.get('contract_type')}\n"

        if job.get('work_mode'):
            response += f"🏠 Mode: {job.get('work_mode')}\n"

        # Afficher le salaire si disponible
        if job.get('salary_min') and job.get('salary_max'):
            currency = job.get('salary_currency', 'EUR')
            response += f"💰 Salaire: {job.get('salary_min')} - {job.get('salary_max')} {currency}\n"

        # Afficher quelques compétences
        skills = job.get('skills', [])
        if skills and isinstance(skills, list):
            skills_str = ', '.join(skills[:3])
            response += f"🔧 Compétences: {skills_str}\n"

        response += "\n"

    response += "💡 Ces offres correspondent à votre recherche. N'hésitez pas à me poser des questions plus spécifiques !"

    return response


@rag_bp.route("/", methods=["POST"])
def chat_with_rag():
    try:
        # --- 1. Récupération des données JSON ---
        data = request.get_json()
        if not data:
            return jsonify({"error": "Données JSON requises"}), 400

        # --- 2. Authentification utilisateur ---
        user_id, user_role, user_data = authenticate_user(data, request)

        if not user_id or not user_role:
            return jsonify({
                "error": "Authentification requise",
                "details": "Fournissez soit un header X-User (base64), soit un objet user dans le body JSON"
            }), 401

        # --- 3. Récupérer les détails utilisateur ---
        display_name, error = get_user_details(user_id, user_role)
        if error:
            return jsonify({"error": error}), 404

        print("[DEBUG] USER ID:", user_id)
        print("[DEBUG] USER ROLE:", user_role)
        print("[DEBUG] USER NAME:", display_name)

        # --- 4. Récupérer query et pageContext ---
        query = data.get("query")
        page_context = data.get("pageContext")

        if not query:
            return jsonify({"error": "Query parameter is required"}), 400

        print("[INFO] Query:", query)
        print("[INFO] Context:", page_context)

        # --- 5. ANALYSE D'INTENTION AVEC LLM ---
        intent_analysis = analyze_user_intent_with_llm(query, page_context, user_role)
        service_to_call = intent_analysis.get("service", "rag_search")
        confidence = intent_analysis.get("confidence", 0.5)

        print(f"[INFO] Service déterminé: {service_to_call} (confiance: {confidence})")
        print(f"[INFO] Raisonnement: {intent_analysis.get('reasoning', 'N/A')}")

        # --- 6. ROUTAGE INTELLIGENT BASÉ SUR L'ANALYSE LLM ---

        if service_to_call == "recommendation_rh" and user_role == "candidate":
            print("[INFO] Service: Recommandation RH pour candidat")
            rh_recommendation_response = recommendation_candidate_rh(user_id, display_name)
            return make_response(
                jsonify({"response": rh_recommendation_response}),
                200,
                {'Content-Type': 'application/json; charset=utf-8'}
            )

        elif service_to_call == "recommendation_jobs" and user_role == "candidate":
            print("[INFO] Service: Recommandation d'offres pour candidat")
            recommendation_response = get_job_recommendations_for_candidate(user_id, display_name)
            return make_response(
                jsonify({"response": recommendation_response}),
                200,
                {'Content-Type': 'application/json; charset=utf-8'}
            )

        elif service_to_call == "recommendation_candidates" and user_role == "recruiter":
            print("[INFO] Service: Recommandation de candidats pour recruteur")
            extracted_data = intent_analysis.get("extracted_data", {})
            job_id = extracted_data.get("job_id") or extract_job_id_from_query(query)

            if job_id:
                recommendation_response = get_candidate_recommendations_for_job(job_id, display_name)
                return make_response(
                    jsonify({"response": recommendation_response}),
                    200,
                    {'Content-Type': 'application/json; charset=utf-8'}
                )
            else:
                return make_response(
                    jsonify({"response": f"Bonjour {display_name} ! Pour vous recommander des candidats, veuillez préciser l'ID de l'offre d'emploi dans votre message."}),
                    200,
                    {'Content-Type': 'application/json; charset=utf-8'}
                )

        elif service_to_call == "general_advice":
            print("[INFO] Service: Conseils généraux")
            specialized_response = generate_specialized_advice(query, page_context, user_role, display_name)
            return make_response(
                jsonify({"response": specialized_response}),
                200,
                {'Content-Type': 'application/json; charset=utf-8'}
            )

        # --- 7. RECHERCHE RAG INTELLIGENTE (fallback) ---
        print("[INFO] Service: Recherche RAG dans la base de données")
        jobs = perform_intelligent_rag_search(query, intent_analysis)

        if not jobs:
            print("[INFO] Aucune offre trouvée, génération de conseils spécialisés")
            specialized_response = generate_specialized_advice(query, page_context, user_role, display_name)
            return make_response(
                jsonify({"response": specialized_response}),
                200,
                {'Content-Type': 'application/json; charset=utf-8'}
            )

        # --- 8. GÉNÉRATION DE RÉPONSE FINALE AVEC LLM + RAG ---
        print(f"[INFO] Génération de réponse avec {len(jobs)} offres trouvées")
        final_response = generate_llm_response_with_rag(query, jobs, user_role, display_name, intent_analysis)

        return make_response(
            jsonify({"response": final_response}),
            200,
            {'Content-Type': 'application/json; charset=utf-8'}
        )

    except Exception as e:
        print("[ERROR]", str(e))
        return jsonify({"error": str(e)}), 500



