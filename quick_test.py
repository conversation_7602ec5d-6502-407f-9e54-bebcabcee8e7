#!/usr/bin/env python3
"""
Test rapide pour vérifier que l'import et la structure fonctionnent
"""

import sys
import os

# Ajouter le répertoire racine au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test des imports"""
    print("🧪 Test des imports...")

    try:
        # Test import des fonctions depuis chat.py
        from app.routes.chat import (
            recommendation_candidate_rh,
            calculate_candidate_job_scores,
            detect_rh_recommendation_intent
        )
        print("✅ Import des fonctions RH depuis chat.py réussi")

        # Vérifier que les fonctions sont bien définies
        assert callable(recommendation_candidate_rh), "recommendation_candidate_rh n'est pas callable"
        assert callable(calculate_candidate_job_scores), "calculate_candidate_job_scores n'est pas callable"
        assert callable(detect_rh_recommendation_intent), "detect_rh_recommendation_intent n'est pas callable"
        print("✅ Toutes les fonctions principales sont bien définies")

        # Test import du service de matching
        from app.services.matching_service import get_matching_service
        assert callable(get_matching_service), "get_matching_service n'est pas callable"
        print("✅ Import du service de matching réussi")

    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

    return True

def test_detection_function():
    """Test de la fonction de détection"""
    print("\n🔍 Test de la fonction de détection...")
    
    try:
        # Import de la fonction de détection depuis chat.py
        from app.routes.chat import detect_rh_recommendation_intent
        
        # Test de différentes queries
        test_cases = [
            ("Comment améliorer mon profil ?", True),
            ("Quelles compétences dois-je développer ?", True),
            ("Conseil pour augmenter mon score", True),
            ("Aide moi à progresser", True),
            ("Recherche emploi Python", False),
            ("Salaire développeur", False),
            ("", False)
        ]
        
        for query, expected in test_cases:
            result = detect_rh_recommendation_intent(query)
            status = "✅" if result == expected else "❌"
            print(f"{status} '{query}' -> {result} (attendu: {expected})")
        
        print("✅ Test de détection terminé")
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    
    return True

def test_structure():
    """Test de la structure des fichiers"""
    print("\n📁 Test de la structure des fichiers...")
    
    files_to_check = [
        "app/services/rh_recommendation_service.py",
        "app/routes/chat.py",
        "test_rh_recommendation.py",
        "test_rh_with_scores.py",
        "example_usage.py",
        "RH_RECOMMENDATION_DOCS.md",
        "IMPLEMENTATION_SUMMARY.md",
        "SCORES_INTEGRATION_SUMMARY.md"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MANQUANT")
    
    return True

def main():
    """Fonction principale"""
    print("🚀 Test rapide de l'implémentation RH")
    print("=" * 50)
    
    # Tests
    success = True
    success &= test_structure()
    success &= test_imports()
    success &= test_detection_function()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Tous les tests sont passés !")
        print("\n📋 Prochaines étapes :")
        print("1. Démarrer le serveur Flask : python app.py")
        print("2. Tester avec de vraies données : python test_rh_recommendation.py")
        print("3. Intégrer dans le frontend")
    else:
        print("❌ Certains tests ont échoué")
        print("Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    main()
