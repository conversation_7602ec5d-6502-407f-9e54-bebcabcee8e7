#!/usr/bin/env python3
"""
Générateur de commandes curl avec header X-User base64
"""

import json
import base64

def generate_curl_command(user_id, role, query="Développeur", page_context="homepage"):
    """Génère une commande curl avec header base64"""
    
    # Créer les données utilisateur
    user_data = {"id": user_id, "role": role}
    user_json = json.dumps(user_data)
    
    # Encoder en base64
    x_user_encoded = base64.b64encode(user_json.encode('utf-8')).decode('utf-8')
    
    # Créer la commande curl
    curl_command = f'''curl -X POST http://127.0.0.1:5000/chat/ \\
  -H "Content-Type: application/json" \\
  -H "X-User: {x_user_encoded}" \\
  -d '{{"query": "{query}", "pageContext": "{page_context}"}}\''''
    
    return curl_command, x_user_encoded

def main():
    print("🚀 Générateur de commandes curl avec header X-User base64")
    print("="*70)
    
    # IDs réels de ta base
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"
    recruiter_id = "6d2df96a-b6b2-4098-b5f3-1192fba544f7"
    
    print("\n📋 1. Test avec candidat réel :")
    print("-" * 40)
    curl_cmd, encoded = generate_curl_command(candidate_id, "candidate", "Développeur Python")
    print(f"Header encodé: {encoded}")
    print(f"Commande curl :")
    print(curl_cmd)
    
    print("\n🏢 2. Test avec recruteur réel :")
    print("-" * 40)
    curl_cmd, encoded = generate_curl_command(recruiter_id, "recruiter", "Frontend Developer")
    print(f"Header encodé: {encoded}")
    print(f"Commande curl :")
    print(curl_cmd)
    
    print("\n❌ 3. Test avec utilisateur inexistant :")
    print("-" * 40)
    fake_id = "00000000-0000-0000-0000-000000000000"
    curl_cmd, encoded = generate_curl_command(fake_id, "candidate", "Test")
    print(f"Header encodé: {encoded}")
    print(f"Commande curl :")
    print(curl_cmd)
    
    print("\n💡 4. Comment encoder manuellement :")
    print("-" * 40)
    print("En Python :")
    print('user_data = {"id": "your_user_id", "role": "candidate"}')
    print('user_json = json.dumps(user_data)')
    print('encoded = base64.b64encode(user_json.encode("utf-8")).decode("utf-8")')
    
    print("\nEn bash :")
    print('echo \'{"id": "your_user_id", "role": "candidate"}\' | base64')
    
    print("\n🔍 5. Comment décoder pour vérifier :")
    print("-" * 40)
    print("En Python :")
    print('decoded = base64.b64decode(encoded).decode("utf-8")')
    print('user_data = json.loads(decoded)')
    
    print("\nEn bash :")
    print('echo "encoded_string" | base64 -d')

if __name__ == "__main__":
    main()
