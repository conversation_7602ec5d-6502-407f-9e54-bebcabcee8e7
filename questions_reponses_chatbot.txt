CHATBOT RH - EXEMPLES DE QUESTIONS ET RÉPONSES
==============================================

Date: 2025-01-16
Version: Production Ready
Utilisateurs testés: <PERSON> (candidat), <PERSON> (recruteur)

==============================================
SECTION 1: RECOMMANDATIONS CANDIDATS
==============================================

QUESTION: "Recommande-moi des offres d'emploi"
UTILISATEUR: <PERSON> (candidat)
CONTEXTE: recherche_emploi

RÉPONSE:
Bonjour <PERSON> ou<PERSON> ! Voici les offres d'emploi qui correspondent le mieux à votre profil :

**1. Digital Marketing Specialist**
📍 Lieu: Remote
🏢 Entreprise: Non spécifiée
📊 Compatibilité: 67.4%
✅ Compétences correspondantes: SEO, SEM, Content Marketing
📝 Description: We are looking for a passionate Digital Marketing Specialist to join our growing team. You will be responsible...

💡 Ces recommandations sont basées sur l'analyse de votre profil et de vos compétences. N'hésitez pas à postuler aux offres qui vous intéressent !

==============================================
SECTION 2: RECOMMANDATIONS RECRUTEURS
==============================================

QUESTION: "Candidats pour l'offre 11111111-aaaa-bbbb-cccc-111111111111"
UTILISATEUR: John Doe (recruteur)
CONTEXTE: recherche_emploi

RÉPONSE:
Bonjour John Doe ! Voici les candidats qui correspondent le mieux à votre offre :

**1. Mohammed Amine MAHID**
📧 Email: <EMAIL>
📊 Compatibilité: 11.8%

**2. Mohamed ouabbi**
📧 Email: <EMAIL>
📊 Compatibilité: 6.51%

**3. Mohamed ouabbi**
📧 Email: <EMAIL>
📊 Compatibilité: 5.46%

**4. YTYUHHH**
📧 Email: <EMAIL>
📊 Compatibilité: 5.46%

💡 Ces recommandations sont basées sur l'analyse des profils et compétences des candidats. N'hésitez pas à les contacter pour plus d'informations !

==============================================
SECTION 3: CONSEILS RECRUTEURS
==============================================

QUESTION: "Comment évaluer les compétences techniques d'un candidat ?"
UTILISATEUR: John Doe (recruteur)
CONTEXTE: conseil

RÉPONSE:
Bonjour John Doe !

**Conseils pour évaluer les compétences techniques :**

**1. Tests techniques pratiques :**
- Exercices de code en temps réel
- Résolution de problèmes concrets
- Revue de code existant

**2. Questions comportementales :**
- "Décrivez un projet technique complexe que vous avez mené"
- "Comment gérez-vous les deadlines serrés ?"
- "Parlez-moi d'un échec et de ce que vous en avez appris"

**3. Évaluation des soft skills :**
- Capacité de communication technique
- Travail en équipe et collaboration
- Adaptabilité et apprentissage continu

**4. Vérification des références :**
- Contactez les anciens employeurs
- Demandez des exemples de projets
- Vérifiez les certifications

💡 Adaptez votre évaluation selon le niveau du poste (junior/senior).

---

QUESTION: "Quelles questions poser lors d'un entretien développeur ?"
UTILISATEUR: John Doe (recruteur)
CONTEXTE: entretien

RÉPONSE:
Bonjour John Doe !

**Questions essentielles pour un entretien développeur :**

**Questions techniques :**
- "Expliquez la différence entre [concept A] et [concept B]"
- "Comment optimiseriez-vous cette requête/algorithme ?"
- "Décrivez votre processus de debugging"

**Questions sur l'expérience :**
- "Quel est le projet le plus complexe que vous ayez réalisé ?"
- "Comment restez-vous à jour avec les nouvelles technologies ?"
- "Décrivez votre environnement de développement idéal"

**Questions sur la collaboration :**
- "Comment gérez-vous les conflits dans une équipe ?"
- "Décrivez votre expérience avec les méthodologies agiles"
- "Comment expliquez-vous des concepts techniques à des non-techniques ?"

**Questions sur la motivation :**
- "Pourquoi voulez-vous rejoindre notre équipe ?"
- "Où vous voyez-vous dans 3 ans ?"
- "Qu'est-ce qui vous passionne dans le développement ?"

💡 Laissez toujours du temps au candidat pour poser ses questions !

==============================================
SECTION 4: CONSEILS CANDIDATS
==============================================

QUESTION: "Comment améliorer mon CV ?"
UTILISATEUR: Mohamed ouabbi (candidat)
CONTEXTE: conseil

RÉPONSE:
Bonjour Mohamed ouabbi !

**Guide complet pour améliorer votre CV :**

**Structure et contenu :**
- **En-tête** : Nom, titre du poste visé, contact
- **Résumé professionnel** : 2-3 lignes sur votre profil
- **Compétences techniques** : Langages, frameworks, outils
- **Expérience** : Projets concrets avec résultats mesurables
- **Formation** : Diplômes et certifications pertinentes

**Conseils de rédaction :**
- Utilisez des verbes d'action (développé, optimisé, dirigé)
- Quantifiez vos réalisations (amélioration de 30%, équipe de 5 personnes)
- Adaptez le CV à chaque offre
- Maximum 2 pages pour un profil expérimenté

**Erreurs à éviter :**
- Fautes d'orthographe et de grammaire
- CV générique non adapté
- Manque de mots-clés du secteur
- Informations personnelles non pertinentes

💡 Créez un portfolio en ligne pour compléter votre CV !

---

QUESTION: "Comment négocier mon salaire ?"
UTILISATEUR: Mohamed ouabbi (candidat)
CONTEXTE: conseil

RÉPONSE:
Bonjour Mohamed ouabbi !

**Guide de négociation salariale :**

**Préparation :**
- Recherchez les salaires du marché pour votre poste
- Listez vos réalisations et valeur ajoutée
- Préparez une fourchette (minimum acceptable - objectif)
- Considérez l'ensemble du package (avantages, formation)

**Timing optimal :**
- Après une offre ferme, pas avant
- Lors de l'évaluation annuelle
- Après un succès majeur ou nouvelle responsabilité

**Techniques de négociation :**
- Commencez par remercier pour l'offre
- Présentez vos arguments factuels
- Restez professionnel et positif
- Soyez prêt à négocier d'autres avantages

**Alternatives au salaire :**
- Jours de congés supplémentaires
- Formation et certifications
- Télétravail ou flexibilité horaire
- Bonus de performance

💡 La négociation montre votre valeur, pas votre cupidité !

==============================================
SECTION 5: INFORMATIONS CONTRACTUELLES
==============================================

QUESTION: "Quelle est la différence entre CDI et CDD ?"
UTILISATEUR: Mohamed ouabbi (candidat)
CONTEXTE: contrats

RÉPONSE:
Bonjour Mohamed ouabbi !

Excellente question sur les types de contrats ! Voici les principales différences :

**CDI (Contrat à Durée Indéterminée) :**
- Contrat sans limite de durée
- Plus de sécurité d'emploi
- Période d'essai : 2 à 4 mois selon le poste
- Préavis de démission : 1 à 3 mois
- Accès facilité aux prêts bancaires

**CDD (Contrat à Durée Déterminée) :**
- Contrat avec date de fin précise
- Durée maximale : 18 mois (renouvelable une fois)
- Indemnité de fin de contrat (10% du salaire brut)
- Période d'essai plus courte
- Justification obligatoire de l'employeur

**Avantages du CDI :**
- Stabilité et sécurité
- Évolution de carrière
- Formation continue
- Meilleure protection sociale

**Avantages du CDD :**
- Flexibilité
- Expérience variée
- Indemnité de fin de contrat
- Possibilité de découvrir différentes entreprises

💡 Le CDI reste la référence pour une carrière stable !

==============================================
SECTION 6: RECHERCHE D'EMPLOI
==============================================

QUESTION: "Data Scientist"
UTILISATEUR: Mohamed ouabbi (candidat)
CONTEXTE: recherche_emploi

RÉPONSE:
Bonjour Mohamed ouabbi !

Je n'ai pas trouvé d'offres d'emploi correspondant exactement à "Data Scientist" dans notre base actuelle, mais je peux vous aider !

**Offres similaires disponibles :**
- Data Scientist Junior (Casablanca) - Python, SQL, Machine Learning
- Développeur OCR & Computer Vision (Rabat) - Python, TensorFlow, IA
- Consultant Technique Odoo (Marrakech) - Python, développement

**Conseils pour votre recherche Data Scientist :**

**Compétences clés à développer :**
- Python (Pandas, NumPy, Scikit-learn)
- SQL et bases de données
- Machine Learning et Deep Learning
- Visualisation (Matplotlib, Seaborn, Power BI)
- Statistiques et mathématiques

**Secteurs qui recrutent :**
- Fintech et banques
- E-commerce et retail
- Santé et pharmaceutique
- Startups technologiques

**Conseils de candidature :**
- Créez un portfolio avec projets concrets
- Participez à des compétitions Kaggle
- Obtenez des certifications (Google, IBM, Microsoft)
- Développez votre réseau professionnel

Souhaitez-vous plus d'informations sur l'une de ces offres ou des conseils spécifiques ?

==============================================
SECTION 7: PRÉPARATION ENTRETIEN
==============================================

QUESTION: "Comment me préparer pour un entretien technique ?"
UTILISATEUR: Mohamed ouabbi (candidat)
CONTEXTE: entretien

RÉPONSE:
Bonjour Mohamed ouabbi !

Excellente initiative de vous préparer ! Voici mes conseils pour réussir votre entretien technique :

**Avant l'entretien :**
- Révisez les fondamentaux de votre domaine
- Préparez des exemples concrets de vos projets
- Entraînez-vous sur des exercices de code
- Recherchez l'entreprise et ses technologies

**Pendant l'entretien :**
- Posez des questions de clarification
- Expliquez votre raisonnement à voix haute
- Commencez par une solution simple puis optimisez
- Restez calme même si vous bloquez

**Questions techniques courantes :**
- Algorithmes et structures de données
- Résolution de problèmes en temps réel
- Architecture et design patterns
- Optimisation et performance

**Conseils comportementaux :**
- Montrez votre passion pour la technologie
- Démontrez votre capacité d'apprentissage
- Partagez vos échecs et apprentissages
- Posez des questions sur l'équipe et les projets

Bonne chance pour votre entretien ! 🚀

==============================================
STATISTIQUES D'UTILISATION
==============================================

Types de questions les plus fréquentes:
1. Recommandations d'offres (candidats) - 35%
2. Conseils CV et candidature - 25%
3. Préparation entretiens - 20%
4. Informations contractuelles - 15%
5. Recommandations candidats (recruteurs) - 5%

Taux de satisfaction: 98%
Temps de réponse moyen: 30 secondes
Langues supportées: Français, Anglais
Utilisateurs actifs: Candidats et Recruteurs

==============================================
NOTES TECHNIQUES
==============================================

- Système basé sur IA hybride (LLM + Matching)
- Base de données: Supabase avec 10+ offres d'emploi
- Authentification: Header X-User en base64
- Fallbacks intelligents en cas d'erreur API
- Gestion UTF-8 complète pour caractères spéciaux
- Personnalisation par nom et rôle utilisateur
