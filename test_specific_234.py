#!/usr/bin/env python3
"""
Test spécifique des tests 2, 3, 4
"""

import requests
import json
import base64

def test_detailed(user_id, role, query, page_context, test_number, description):
    """Test détaillé d'une query spécifique"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Encoder le header utilisateur
    user_data = {"id": user_id, "role": role}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": query,
        "pageContext": page_context
    }
    
    print(f"🧪 TEST {test_number}: {description}")
    print(f"👤 Utilisateur: {role}")
    print(f"💬 Query: {query}")
    print(f"📋 Context: {page_context}")
    print("-" * 70)
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            response_text = result['response']
            
            print(f"✅ SUCCÈS!")
            print(f"📏 Longueur réponse: {len(response_text)} caractères")
            
            # Analyser le contenu
            if "**1." in response_text:
                count = response_text.count("**") // 2
                print(f"📈 Nombre d'éléments structurés: {count}")
            
            if "Bonjour" in response_text:
                greeting = response_text.split('\n')[0]
                print(f"👋 Salutation: {greeting}")
            
            if "Compatibilité:" in response_text:
                print(f"🎯 Type: Recommandation avec scores")
            elif "conseil" in response_text.lower():
                print(f"💡 Type: Conseil/Guidance")
            elif any(word in response_text for word in ["offre", "poste", "emploi"]):
                print(f"🔍 Type: Recherche d'emploi")
            
            print(f"\n📝 RÉPONSE COMPLÈTE:")
            print("=" * 70)
            print(response_text)
            print("=" * 70)
            
        else:
            error = response.json()
            print(f"❌ ERREUR: {error}")
            
    except Exception as e:
        print(f"💥 EXCEPTION: {e}")
    
    print("\n" + "🔚" * 35 + "\n")

def main():
    print("🎯 TEST DÉTAILLÉ DES FONCTIONNALITÉS 2, 3, 4")
    print("=" * 70)
    
    # IDs des utilisateurs
    recruiter_id = "e714bb4b-b3f8-4343-8d5d-1e120e421b29"  # John Doe
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"  # Mohamed ouabbi
    
    # Test 2: Recruteur - Recommandations avec job ID
    test_detailed(
        recruiter_id, 
        "recruiter",
        "Candidats pour l'offre 11111111-aaaa-bbbb-cccc-111111111111", 
        "recherche_emploi",
        2,
        "Recommandations recruteur pour Data Scientist Junior"
    )
    
    # Test 3: Candidat - Recherche Data Scientist
    test_detailed(
        candidate_id, 
        "candidate",
        "Data Scientist", 
        "recherche_emploi",
        3,
        "Recherche Data Scientist par candidat"
    )
    
    # Test 4: Candidat - Conseil CV
    test_detailed(
        candidate_id, 
        "candidate",
        "Comment améliorer mon CV ?", 
        "conseil",
        4,
        "Conseil amélioration CV"
    )

if __name__ == "__main__":
    main()
