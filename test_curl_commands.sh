#!/bin/bash

# Curl commands to test your APIs with the provided data
# Make sure your Flask server is running on localhost:5000

BASE_URL="http://localhost:5000"

echo "🚀 Testing APIs with curl commands"
echo "=================================="

# Test 1: Get all jobs
echo -e "\n📋 1. Get all jobs:"
curl -X GET "$BASE_URL/job" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 2: Get specific job by ID (Data Scientist Junior)
echo -e "\n🔍 2. Get Data Scientist Junior job:"
curl -X GET "$BASE_URL/job/11111111-aaaa-bbbb-cccc-111111111111" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 3: Get specific job by ID (OCR Developer)
echo -e "\n🔍 3. Get OCR Developer job:"
curl -X GET "$BASE_URL/job/22222222-bbbb-cccc-dddd-222222222222" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 4: Get specific job by ID (Senior Full Stack)
echo -e "\n🔍 4. Get Senior Full Stack job:"
curl -X GET "$BASE_URL/job/2b7dbbf0-10ee-4d05-b588-640a0762e3e3" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 5: Search jobs with Python
echo -e "\n🔍 5. Search jobs with 'Python':"
curl -X GET "$BASE_URL/job?search=Python&limit=5" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 6: Search jobs with React
echo -e "\n🔍 6. Search jobs with 'React':"
curl -X GET "$BASE_URL/job?search=React&limit=5" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 7: Filter jobs by location (Remote)
echo -e "\n🌍 7. Filter jobs by location 'Remote':"
curl -X GET "$BASE_URL/job?location=Remote&limit=5" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 8: Filter jobs by location (Casablanca)
echo -e "\n🌍 8. Filter jobs by location 'Casablanca':"
curl -X GET "$BASE_URL/job?location=Casablanca&limit=5" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 9: Filter jobs by contract type (CDI)
echo -e "\n📄 9. Filter jobs by contract type 'CDI':"
curl -X GET "$BASE_URL/job?contract_type=CDI&limit=5" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 10: Filter jobs by minimum salary
echo -e "\n💰 10. Filter jobs by minimum salary 50000:"
curl -X GET "$BASE_URL/job?min_salary=50000&limit=5" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 11: Test pagination
echo -e "\n📄 11. Test pagination (page 1, limit 3):"
curl -X GET "$BASE_URL/job?page=1&limit=3" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 12: Test pagination (page 2, limit 3)
echo -e "\n📄 12. Test pagination (page 2, limit 3):"
curl -X GET "$BASE_URL/job?page=2&limit=3" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 13: Get recommended jobs
echo -e "\n🎯 13. Get recommended jobs:"
curl -X GET "$BASE_URL/job/recommended" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 14: Get all candidates
echo -e "\n👥 14. Get all candidates:"
curl -X GET "$BASE_URL/candidates/" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 15: Combined filters
echo -e "\n🔧 15. Combined filters (developer + Remote + CDI):"
curl -X GET "$BASE_URL/job?search=developer&location=Remote&contract_type=CDI&limit=5" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 16: Test work mode filter
echo -e "\n🏠 16. Filter by work mode (Remote):"
curl -X GET "$BASE_URL/job?work_mode=Remote&limit=5" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Test 17: Test invalid job ID (should return 404)
echo -e "\n❌ 17. Test invalid job ID (should return 404):"
curl -X GET "$BASE_URL/job/invalid-job-id" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

echo -e "\n✅ All curl tests completed!"
echo "Check the responses above to verify your APIs are working correctly."
