#!/usr/bin/env python3
"""
Test simple de votre requete exacte
"""

import requests
import json
import time

def test_server_health():
    """Test si le serveur repond"""
    try:
        response = requests.get("http://localhost:5000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def test_your_exact_request():
    """Test avec votre requete exacte"""
    
    url = "http://localhost:5000/chat/"
    
    # Votre requete exacte
    headers = {
        "accept": "application/json",
        "content-type": "application/json"
    }
    
    body = {
        "query": " hi*",
        "pageContext": "",
        "role": "candidate",
        "user": {
            "id": "63800628-55a2-4c33-97ce-5bcc5ee6a1bb",
            "role": "candidate"
        }
    }
    
    print("🧪 Test de votre requete exacte")
    print("=" * 40)
    print(f"URL: {url}")
    print(f"Query: '{body['query']}'")
    print(f"User: {body['user']['id'][:8]}...")
    print("=" * 40)
    
    try:
        print("📡 Envoi de la requete...")
        response = requests.post(url, headers=headers, json=body, timeout=30)
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS!")
            
            try:
                json_response = response.json()
                response_text = json_response.get('response', '')
                
                print(f"\n📝 Reponse ({len(response_text)} caracteres):")
                print("-" * 40)
                print(response_text)
                print("-" * 40)
                
                # Verifications basiques
                checks = {
                    "Contient le nom": "Taoufiq" in response_text,
                    "Reponse en francais": any(word in response_text.lower() for word in ["bonjour", "salut", "hello"]),
                    "Reponse non vide": len(response_text) > 10,
                    "Format JSON valide": True
                }
                
                print("\n🔍 Verifications:")
                for check, passed in checks.items():
                    status = "✅" if passed else "❌"
                    print(f"  {status} {check}")
                
                score = sum(checks.values()) / len(checks) * 100
                print(f"\n🎯 Score: {score:.0f}%")
                
                return True
                
            except json.JSONDecodeError:
                print("❌ Reponse JSON invalide")
                print(f"Raw response: {response.text}")
                return False
                
        else:
            print(f"❌ FAILED - Status: {response.status_code}")
            try:
                error = response.json()
                print(f"Error: {error}")
            except:
                print(f"Raw error: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Serveur non accessible")
        print("💡 Demarrez le serveur avec: python run.py")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Test simple de votre requete chat")
    
    # Verifier si le serveur repond
    print("🔍 Verification du serveur...")
    if test_server_health():
        print("✅ Serveur accessible")
    else:
        print("❌ Serveur non accessible")
        print("💡 Demarrez le serveur avec: python run.py")
        return False
    
    # Attendre un peu
    time.sleep(1)
    
    # Test de votre requete
    success = test_your_exact_request()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 Test reussi!")
        print("✅ Votre integration LLM fonctionne")
    else:
        print("❌ Test echoue")
        print("🔧 Verifiez les logs du serveur")
    
    return success

if __name__ == "__main__":
    main()
