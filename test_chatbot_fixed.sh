#!/bin/bash

URL="http://127.0.0.1:5000/chat/"
HEADER="Content-Type: application/json"

USER_JSON='{"id":"df82c6a7-72cd-4e06-ad09-5a4d0127bca4","role":"candidate"}'
# Utiliser base64 avec -w 0 pour éviter les retours à la ligne (Linux)
# Ou utiliser tr -d '\n' pour supprimer les retours à la ligne (compatible)
USER_B64=$(echo -n "$USER_JSON" | base64 | tr -d '\n')

echo "🔍 Debug info:"
echo "USER_JSON: $USER_JSON"
echo "USER_B64: $USER_B64"
echo "USER_B64 length: ${#USER_B64}"
echo ""

# Test de décodage pour vérifier
echo "🧪 Test de décodage:"
DECODED=$(echo "$USER_B64" | base64 -d 2>/dev/null || echo "ERREUR DE DECODAGE")
echo "Décodé: $DECODED"
echo ""

declare -a QUERIES=(
  '{"query": "Comment améliorer mon CV pour un poste de data scientist ?", "pageContext": "conseil"}'
  '{"query": "Que dois-je mettre dans une lettre de motivation pour une startup tech ?", "pageContext": "lettre_motivation"}'
  '{"query": "Quelle est la différence entre un CDI et un CDD ?", "pageContext": "contrats"}'
  '{"query": "Comment me préparer à un entretien développeur backend ?", "pageContext": "entretien"}'
  '{"query": "Est-ce qu'il faut apprendre Docker pour trouver un job de développeur ?", "pageContext": "skills"}'
)

for i in "${!QUERIES[@]}"
do
  query="${QUERIES[$i]}"
  echo "🤖 Test $((i+1))/5:"
  echo ">>> Question: $query"
  
  response=$(curl -s -X POST "$URL" \
    -H "$HEADER" \
    -H "X-User: $USER_B64" \
    -d "$query")
  
  echo ">>> Réponse:"
  echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
  echo -e "\n" + "="*60 + "\n"
done
