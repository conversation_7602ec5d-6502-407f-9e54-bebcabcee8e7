#!/usr/bin/env python3
"""
Test spécifique avec le candidat <PERSON> ou<PERSON>
"""

import sys
import os
import json
import base64
import requests

# Ajouter le répertoire racine au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Données du candidat réel
CANDIDATE_ID = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"
CANDIDATE_NAME = "<PERSON> ouabbi"

def test_direct_function_call():
    """Test direct de la fonction avec le candidat spécifique"""
    print(f"🎯 Test direct avec {CANDIDATE_NAME}")
    print("=" * 50)
    
    try:
        from app import create_app
        from app.routes.chat import recommendation_candidate_rh
        
        app = create_app()
        
        with app.app_context():
            print(f"✅ Candidat: {CANDIDATE_NAME}")
            print(f"✅ ID: {CANDIDATE_ID}")
            
            # Appeler directement la fonction
            print("\n🔄 Génération des recommandations...")
            result = recommendation_candidate_rh(CANDIDATE_ID, CANDIDATE_NAME)
            
            if result and isinstance(result, str):
                print("✅ Recommandations générées avec succès!")
                print(f"✅ Longueur: {len(result)} caractères")
                
                # Afficher la réponse complète
                print("\n" + "="*60)
                print("📄 RECOMMANDATIONS COMPLÈTES")
                print("="*60)
                print(result)
                print("="*60)
                
                # Vérifier les éléments clés
                key_elements = [
                    "ANALYSE DE VOTRE PROFIL",
                    "SCORE ACTUEL MOYEN",
                    "MEILLEURES CORRESPONDANCES",
                    "POINTS FORTS",
                    "COMPÉTENCES À DÉVELOPPER",
                    "RECOMMANDATIONS PERSONNALISÉES"
                ]
                
                found_elements = [elem for elem in key_elements if elem in result]
                print(f"\n✅ Éléments trouvés: {len(found_elements)}/{len(key_elements)}")
                for elem in found_elements:
                    print(f"   ✓ {elem}")
                
                return True
            else:
                print("❌ Aucune recommandation générée")
                return False
                
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_via_http_api():
    """Test via l'API HTTP"""
    print(f"\n🌐 Test via API HTTP avec {CANDIDATE_NAME}")
    print("=" * 50)
    
    # Configuration
    BASE_URL = "http://localhost:5000"
    ENDPOINT = f"{BASE_URL}/api/chat/"
    
    # Données utilisateur
    user_data = {
        "id": CANDIDATE_ID,
        "role": "candidate"
    }
    
    # Encoder en base64
    user_json = json.dumps(user_data)
    user_b64 = base64.b64encode(user_json.encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    # Différentes queries de test
    test_queries = [
        "Comment améliorer mon profil ?",
        "Quelles compétences dois-je développer ?",
        "Conseil pour augmenter mon score de matching",
        "Aide moi à progresser dans ma carrière"
    ]
    
    successful_tests = 0
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n--- Test {i}: '{query}' ---")
        
        data = {
            "query": query,
            "pageContext": "conseil"
        }
        
        try:
            response = requests.post(ENDPOINT, headers=headers, json=data, timeout=120)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                response_text = response_data.get('response', '')
                
                if "ANALYSE DE VOTRE PROFIL" in response_text:
                    print("✅ Recommandations RH détectées et générées")
                    print(f"✅ Longueur: {len(response_text)} caractères")
                    
                    # Afficher un extrait
                    print(f"Extrait: {response_text[:200]}...")
                    successful_tests += 1
                else:
                    print("⚠️  Réponse reçue mais pas de recommandations RH")
                    print(f"Réponse: {response_text[:200]}...")
            else:
                print(f"❌ Erreur HTTP: {response.json()}")
                
        except requests.Timeout:
            print("⏰ Timeout - Le calcul prend trop de temps")
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    print(f"\n📊 Résultats API: {successful_tests}/{len(test_queries)} tests réussis")
    return successful_tests > 0

def test_candidate_profile_analysis():
    """Analyse du profil du candidat"""
    print(f"\n👤 Analyse du profil de {CANDIDATE_NAME}")
    print("=" * 50)
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.app_context():
            supabase = app.supabase
            
            # Récupérer le profil candidat
            profile = supabase.table("candidate_profiles") \
                .select("skillner_skills, py_skills, added_skills, skills, location") \
                .eq("candidate_id", CANDIDATE_ID) \
                .execute()
            
            if profile.data:
                profile_data = profile.data[0]
                
                print(f"✅ Profil trouvé pour {CANDIDATE_NAME}")
                
                # Analyser les compétences
                all_skills = set()
                skill_sources = ['skillner_skills', 'py_skills', 'added_skills', 'skills']
                
                for source in skill_sources:
                    skills_list = profile_data.get(source)
                    if skills_list and isinstance(skills_list, list):
                        skills_count = len(skills_list)
                        print(f"✅ {source}: {skills_count} compétences")
                        if skills_count > 0:
                            print(f"   Exemples: {skills_list[:5]}")
                        all_skills.update([skill.strip().lower() for skill in skills_list if skill and skill.strip()])
                
                print(f"\n✅ Total compétences uniques: {len(all_skills)}")
                print(f"✅ Localisation: {profile_data.get('location', 'Non spécifiée')}")
                
                # Analyser les jobs disponibles
                jobs = supabase.table("jobs") \
                    .select("id, title, skills, requirements") \
                    .eq("is_active", True) \
                    .execute()
                
                if jobs.data:
                    print(f"\n✅ {len(jobs.data)} offres d'emploi actives trouvées")
                    
                    # Analyser les compétences demandées
                    market_skills = {}
                    for job in jobs.data:
                        job_skills = (job.get('skills', []) or []) + (job.get('requirements', []) or [])
                        for skill in job_skills:
                            if skill and isinstance(skill, str):
                                skill_clean = skill.strip().lower()
                                if skill_clean:
                                    market_skills[skill_clean] = market_skills.get(skill_clean, 0) + 1
                    
                    # Correspondances
                    matching_skills = all_skills & set(market_skills.keys())
                    missing_skills = set(market_skills.keys()) - all_skills
                    
                    print(f"✅ Compétences correspondantes: {len(matching_skills)}")
                    if matching_skills:
                        top_matching = sorted(matching_skills, key=lambda x: market_skills.get(x, 0), reverse=True)[:5]
                        print(f"   Top 5: {top_matching}")
                    
                    print(f"✅ Compétences manquantes: {len(missing_skills)}")
                    if missing_skills:
                        top_missing = sorted(missing_skills, key=lambda x: market_skills.get(x, 0), reverse=True)[:5]
                        print(f"   Top 5 demandées: {top_missing}")
                    
                    # Calcul du taux de correspondance
                    if market_skills:
                        match_rate = len(matching_skills) / len(market_skills) * 100
                        print(f"✅ Taux de correspondance estimé: {match_rate:.1f}%")
                
                return True
            else:
                print(f"❌ Aucun profil trouvé pour {CANDIDATE_NAME}")
                return False
                
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🧪 Test Spécifique - Mohamed ouabbi")
    print("=" * 60)
    
    print(f"📋 Candidat de test:")
    print(f"   Nom: {CANDIDATE_NAME}")
    print(f"   ID: {CANDIDATE_ID}")
    
    tests_passed = 0
    total_tests = 3
    
    # Test d'analyse du profil
    if test_candidate_profile_analysis():
        tests_passed += 1
    
    # Test direct de la fonction
    if test_direct_function_call():
        tests_passed += 1
    
    # Test via API HTTP (optionnel si serveur démarré)
    print("\n⚠️  Pour tester l'API HTTP, assurez-vous que le serveur Flask est démarré")
    test_api = input("Voulez-vous tester l'API HTTP ? (y/N): ")
    if test_api.lower() == 'y':
        if test_via_http_api():
            tests_passed += 1
        total_tests += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Résultats finaux: {tests_passed}/{total_tests} tests réussis")
    
    if tests_passed >= 2:
        print("🎉 Tests principaux réussis!")
        print(f"\n✅ La fonction recommendation_candidate_rh fonctionne correctement avec {CANDIDATE_NAME}")
        print("\n🚀 Prochaines étapes:")
        print("1. Intégrer dans le frontend")
        print("2. Tester avec d'autres candidats")
        print("3. Optimiser les performances si nécessaire")
    else:
        print("❌ Tests principaux échoués")
        print("Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    main()
