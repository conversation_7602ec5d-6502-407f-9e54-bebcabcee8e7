#!/usr/bin/env python3
"""
Test avancé pour la fonction recommendation_candidate_rh avec scores réels
"""

import requests
import json
import base64

# Configuration
BASE_URL = "http://localhost:5000"
CHAT_ENDPOINT = f"{BASE_URL}/api/chat/"

# Données de test - remplacez par un vrai candidat de votre base
TEST_CANDIDATE = {
    "id": "your-candidate-uuid-here",  # Remplacez par un UUID réel
    "role": "candidate"
}

def test_rh_recommendations_with_real_scores():
    """Test des recommandations RH avec scores réels"""
    print("=== Test Recommandations RH avec Scores Réels ===")
    
    # Encoder les données utilisateur en base64
    user_data_json = json.dumps(TEST_CANDIDATE)
    user_data_b64 = base64.b64encode(user_data_json.encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_data_b64
    }
    
    # Différentes queries de test pour les recommandations RH
    test_queries = [
        {
            "query": "Comment améliorer mon profil avec des scores précis ?",
            "description": "Test avec demande explicite de scores"
        },
        {
            "query": "Analyse détaillée de mes compétences",
            "description": "Test d'analyse approfondie"
        },
        {
            "query": "Quelles sont mes lacunes par rapport au marché ?",
            "description": "Test d'identification des gaps"
        },
        {
            "query": "Conseil pour augmenter mon score de matching",
            "description": "Test d'optimisation du matching"
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n--- Test {i}: {test_case['description']} ---")
        print(f"Query: '{test_case['query']}'")
        
        data = {
            "query": test_case['query'],
            "pageContext": "conseil"
        }
        
        try:
            response = requests.post(CHAT_ENDPOINT, headers=headers, json=data)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                response_text = response_data.get('response', '')
                
                # Vérifier si c'est bien une recommandation RH
                if "ANALYSE DE VOTRE PROFIL" in response_text:
                    print("✅ Recommandation RH détectée")
                    
                    # Vérifier la présence d'éléments spécifiques aux scores réels
                    score_indicators = [
                        "SCORE ACTUEL MOYEN",
                        "MEILLEURES CORRESPONDANCES",
                        "IMPACT POTENTIEL",
                        "RECOMMANDATIONS PERSONNALISÉES"
                    ]
                    
                    found_indicators = [ind for ind in score_indicators if ind in response_text]
                    print(f"✅ Indicateurs de scores trouvés: {len(found_indicators)}/{len(score_indicators)}")
                    
                    if found_indicators:
                        print(f"   - {', '.join(found_indicators)}")
                    
                    # Afficher un extrait de la réponse
                    print(f"Extrait de réponse: {response_text[:300]}...")
                    
                    if len(found_indicators) >= 3:
                        print("🎉 Test réussi - Recommandations avec scores réels")
                    else:
                        print("⚠️  Test partiellement réussi - Certains éléments manquent")
                else:
                    print("❌ Recommandation RH non détectée")
                    print(f"Réponse reçue: {response_text[:200]}...")
            else:
                print(f"❌ Test échoué: {response.json()}")
                
        except Exception as e:
            print(f"❌ Erreur lors du test: {e}")

def test_score_accuracy():
    """Test pour vérifier la précision des scores"""
    print("\n=== Test Précision des Scores ===")
    
    user_data_json = json.dumps(TEST_CANDIDATE)
    user_data_b64 = base64.b64encode(user_data_json.encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_data_b64
    }
    
    data = {
        "query": "Analyse précise de mon score de matching",
        "pageContext": "conseil"
    }
    
    try:
        response = requests.post(CHAT_ENDPOINT, headers=headers, json=data)
        
        if response.status_code == 200:
            response_data = response.json()
            response_text = response_data.get('response', '')
            
            # Rechercher des patterns de scores
            import re
            
            # Chercher des pourcentages
            percentages = re.findall(r'(\d+\.?\d*)%', response_text)
            
            # Chercher des scores numériques
            scores = re.findall(r'score[^:]*:\s*(\d+\.?\d*)', response_text, re.IGNORECASE)
            
            print(f"✅ Pourcentages trouvés: {len(percentages)}")
            if percentages:
                print(f"   - Exemples: {percentages[:5]}")
            
            print(f"✅ Scores trouvés: {len(scores)}")
            if scores:
                print(f"   - Exemples: {scores[:3]}")
            
            # Vérifier la cohérence des scores
            if percentages:
                try:
                    numeric_percentages = [float(p) for p in percentages if float(p) <= 100]
                    if numeric_percentages:
                        avg_percentage = sum(numeric_percentages) / len(numeric_percentages)
                        print(f"✅ Score moyen détecté: {avg_percentage:.1f}%")
                        
                        if 0 <= avg_percentage <= 100:
                            print("✅ Scores dans une plage valide")
                        else:
                            print("⚠️  Scores hors plage normale")
                except ValueError:
                    print("⚠️  Erreur de parsing des scores")
            
        else:
            print(f"❌ Erreur: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_performance():
    """Test de performance des recommandations"""
    print("\n=== Test Performance ===")
    
    import time
    
    user_data_json = json.dumps(TEST_CANDIDATE)
    user_data_b64 = base64.b64encode(user_data_json.encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_data_b64
    }
    
    data = {
        "query": "Recommandations rapides pour mon profil",
        "pageContext": "conseil"
    }
    
    try:
        start_time = time.time()
        response = requests.post(CHAT_ENDPOINT, headers=headers, json=data, timeout=30)
        end_time = time.time()
        
        duration = end_time - start_time
        
        print(f"⏱️  Temps de réponse: {duration:.2f} secondes")
        
        if duration < 10:
            print("✅ Performance excellente (<10s)")
        elif duration < 20:
            print("✅ Performance acceptable (<20s)")
        else:
            print("⚠️  Performance lente (>20s)")
        
        if response.status_code == 200:
            response_data = response.json()
            response_length = len(response_data.get('response', ''))
            print(f"📝 Longueur de réponse: {response_length} caractères")
            
            if response_length > 500:
                print("✅ Réponse détaillée")
            else:
                print("⚠️  Réponse courte")
        
    except requests.Timeout:
        print("❌ Timeout - Réponse trop lente")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def main():
    """Fonction principale"""
    print("🧪 Tests avancés - Recommandations RH avec Scores Réels")
    print("=" * 60)
    
    print("\n⚠️  IMPORTANT: Assurez-vous que:")
    print("1. Le serveur Flask est démarré")
    print("2. Vous avez remplacé 'your-candidate-uuid-here' par un UUID réel")
    print("3. Le candidat existe dans votre base de données")
    print("4. Le candidat a un profil avec des compétences")
    print("5. Il y a des offres d'emploi actives dans la base")
    
    # Demander confirmation
    confirm = input("\nVoulez-vous continuer les tests ? (y/N): ")
    if confirm.lower() != 'y':
        print("Tests annulés.")
        return
    
    # Exécuter les tests
    test_rh_recommendations_with_real_scores()
    test_score_accuracy()
    test_performance()
    
    print("\n" + "=" * 60)
    print("🏁 Tests terminés")
    print("\n💡 Si les tests échouent:")
    print("- Vérifiez que le candidat a des compétences dans son profil")
    print("- Vérifiez qu'il y a des offres d'emploi actives")
    print("- Consultez les logs du serveur pour plus de détails")

if __name__ == "__main__":
    main()
