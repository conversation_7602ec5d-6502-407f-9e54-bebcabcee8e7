#!/usr/bin/env python3
"""
Test de la correction d'encodage
"""

import requests
import json
import base64

def test_encoding_fix():
    """Test du problème d'encodage corrigé"""
    url = "http://127.0.0.1:5000/chat/"
    
    # Candidat <PERSON>
    candidate_id = "ed15f200-9b19-4bde-819d-f3b9a29b35e3"
    
    # Encoder le header utilisateur
    user_data = {"id": candidate_id, "role": "candidate"}
    user_b64 = base64.b64encode(json.dumps(user_data).encode('utf-8')).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "X-User": user_b64
    }
    
    data = {
        "query": "Data Scientist",
        "pageContext": "recherche_emploi"
    }
    
    print("🔧 Test de la correction d'encodage")
    print("=" * 50)
    print("👤 Candidat: <PERSON>")
    print("💬 Query: Data Scientist")
    print("📋 Context: recherche_emploi")
    print("-" * 50)
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            response_text = result['response']
            
            print(f"✅ SUCCÈS! Problème d'encodage résolu!")
            print(f"📏 Longueur réponse: {len(response_text)} caractères")
            
            if "Bonjour" in response_text:
                greeting = response_text.split('\n')[0]
                print(f"👋 Salutation: {greeting}")
            
            print(f"\n📝 RÉPONSE:")
            print("=" * 50)
            if len(response_text) > 500:
                print(f"{response_text[:500]}...")
                print(f"\n[Réponse complète: {len(response_text)} caractères]")
            else:
                print(response_text)
            print("=" * 50)
            
        else:
            error = response.json()
            print(f"❌ ERREUR PERSISTANTE: {error}")
            
    except Exception as e:
        print(f"💥 EXCEPTION: {e}")

def main():
    test_encoding_fix()

if __name__ == "__main__":
    main()
