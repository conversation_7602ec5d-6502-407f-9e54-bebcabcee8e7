-- Migration pour mettre à jour les recruiter_id manquants dans la table jobs
-- Exécuter cette requête pour remplir les recruiter_id null

-- 1. Vérifier l'état actuel
SELECT 
    COUNT(*) as total_jobs,
    COUNT(recruiter_id) as jobs_with_recruiter,
    COUNT(*) - COUNT(recruiter_id) as jobs_without_recruiter
FROM jobs;

-- 2. Voir les jobs sans recruiter_id et leurs companies
SELECT 
    j.id,
    j.title,
    j.company_id,
    c.name as company_name,
    c.recruiter_id
FROM jobs j
LEFT JOIN companies c ON j.company_id = c.id
WHERE j.recruiter_id IS NULL;

-- 3. Mettre à jour les jobs sans recruiter_id
UPDATE jobs 
SET recruiter_id = companies.recruiter_id 
FROM companies 
WHERE jobs.company_id = companies.id 
AND jobs.recruiter_id IS NULL
AND companies.recruiter_id IS NOT NULL;

-- 4. Vérifier le résultat après mise à jour
SELECT 
    COUNT(*) as total_jobs,
    COUNT(recruiter_id) as jobs_with_recruiter,
    COUNT(*) - COUNT(recruiter_id) as jobs_without_recruiter
FROM jobs;

-- 5. Afficher les jobs qui n'ont toujours pas de recruiter_id
-- (ceux dont la company n'a pas de recruiter_id)
SELECT 
    j.id,
    j.title,
    j.company_id,
    c.name as company_name,
    'Company has no recruiter' as reason
FROM jobs j
LEFT JOIN companies c ON j.company_id = c.id
WHERE j.recruiter_id IS NULL;
