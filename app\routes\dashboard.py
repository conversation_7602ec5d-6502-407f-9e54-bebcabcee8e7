from flask import Blueprint, jsonify, current_app,request
from datetime import datetime
from collections import defaultdict, OrderedDict
import calendar

dashboard_bp = Blueprint("dashboard", __name__) #*
#*
#**
@dashboard_bp.route("/dashboard/summary", methods=["GET"])#used
def dashboard_summary():
    supabase = current_app.supabase

    total_applied = supabase.table("applications").select("id").execute().data
    total_hired = supabase.table("applications").select("id").eq("status", "hired").execute().data
    open_positions = supabase.table("jobs").select("id").execute().data
    avg_cost_per_hire =600
    return jsonify({
        "appliedCandidates": len(total_applied),
        "hiredCandidates": len(total_hired),
        "openPositions": len(open_positions),
        "avgCostPerHire": avg_cost_per_hire ,
    }), 200

@dashboard_bp.route("/dashboard/job-stats", methods=["GET"])
def job_stats():
    supabase = current_app.supabase
    apps = supabase.table("applications").select("created_at,status").execute().data

    # Initialiser tous les mois à 0
    stats = {calendar.month_abbr[m]: {"applications": 0, "hired": 0} for m in range(1, 13)}

    for app in apps:
        created_at = app["created_at"]
        try:
            dt = datetime.strptime(created_at, "%Y-%m-%dT%H:%M:%S.%f")
        except ValueError:
            dt = datetime.strptime(created_at, "%Y-%m-%dT%H:%M:%S")
        
        month = dt.strftime("%b")
        stats[month]["applications"] += 1
        if app["status"] == "hired":
            stats[month]["hired"] += 1

    # Forcer le retour de tous les mois, même s’il n’y a aucune application
    ordered_stats = [{"month": calendar.month_abbr[m], **stats[calendar.month_abbr[m]]} for m in range(1, 13)]

    return jsonify(ordered_stats), 200

"""
@dashboard_bp.route("/dashboard/application-progression", methods=["GET"])
def application_progression():
    supabase = current_app.supabase
    apps = supabase.table("applications").select("status").execute().data

    counts = defaultdict(int)
    for app in apps:
        counts[app["status"]] += 1

    return jsonify({
        "applied": counts.get("applied", 0),
        "shortlisted_round1": counts.get("shortlisted_round1", 0),
        "shortlisted_round2": counts.get("shortlisted_round2", 0),
        "hired": counts.get("hired", 0)
    }), 200

"""



@dashboard_bp.route("/dashboard/offer-acceptance", methods=["GET"])#used - now fully dynamic
def offer_acceptance():
    supabase = current_app.supabase
    apps = supabase.table("applications").select("status").execute().data

    accepted = sum(1 for app in apps if app["status"] == "hired")
    rejected = sum(1 for app in apps if app["status"] == "dismissed")
    pending = sum(1 for app in apps if app["status"] == "pending")
    total = accepted + rejected + pending

    # Return completely dynamic data based on actual database content
    if not apps:
        return jsonify({
            "accepted": 0,
            "rejected": 0,
            "pending": 0,
            "total": 0
        }), 200

    return jsonify({
        "accepted": round((accepted / len(apps)) * 100),
        "rejected": round((rejected / len(apps)) * 100),
        "pending": round((pending / len(apps)) * 100),
        "total": round((total / len(apps)) * 100),
    }), 200

@dashboard_bp.route("/dashboard/source-of-hire", methods=["GET"])#now dynamic
def source_of_hire():
    supabase = current_app.supabase
    profiles = supabase.table("candidate_profiles").select("source").execute().data

    counts = defaultdict(int)
    for profile in profiles:
        source = profile.get("source", "other")
        counts[source] += 1

    total = sum(counts.values())
    if total == 0:
        return jsonify([]), 200

    response = [
        {"source": k, "count": v, "percentage": round((v / total) * 100)} for k, v in counts.items()
    ]
    return jsonify(response), 200
@dashboard_bp.route("/dashboard/lgbtq-inclusion", methods=["GET"])  # Estimation basée sur statistiques démographiques
def lgbtq_inclusion():
    supabase = current_app.supabase

    try:
        # Récupérer toutes les applications pour calculer les statistiques d'inclusion
        applications = supabase.table("applications").select("candidate_id, status").execute().data

        if not applications:
            return jsonify({
                "applied": 0,
                "interviewed": 0,
                "hired": 0,
                "debug": "No applications found in database"
            }), 200

        # Compter les candidats uniques qui ont postulé
        unique_candidates = set(app["candidate_id"] for app in applications if app["candidate_id"])
        total_applied = len(unique_candidates)

        # Compter les candidats embauchés (hired) et ceux qui ont matché
        hired_candidates = set(app["candidate_id"] for app in applications if app["status"] == "hired" and app["candidate_id"])
        matched_candidates = set(app["candidate_id"] for app in applications if app["status"] == "matched" and app["candidate_id"])
        total_hired = len(hired_candidates)
        total_matched = len(matched_candidates)

        # Compter les candidats qui ont eu des entretiens
        interviews = supabase.table("interviews").select("candidate_id").execute().data
        interviewed_candidates = set(interview["candidate_id"] for interview in interviews if interview["candidate_id"])
        total_interviewed = len(interviewed_candidates)

        # Si aucune donnée, retourner des informations de debug
        if total_applied == 0 and total_interviewed == 0 and total_hired == 0:
            return jsonify({
                "applied": 0,
                "interviewed": 0,
                "hired": 0,
                "debug": {
                    "total_applications": len(applications),
                    "total_interviews": len(interviews),
                    "applications_sample": applications[:3] if applications else [],
                    "interviews_sample": interviews[:3] if interviews else []
                }
            }), 200

        # Estimation LGBTQ+ basée sur les statistiques démographiques générales
        # Environ 5-7% de la population s'identifie comme LGBTQ+
        lgbtq_percentage = 0.06  # 6% estimation

        # Calculer les estimations LGBTQ+ (minimum 1 si les totaux > 0)
        estimated_applied = max(1, round(total_applied * lgbtq_percentage)) if total_applied > 0 else 0
        estimated_interviewed = max(1, round(total_interviewed * lgbtq_percentage)) if total_interviewed > 0 else 0
        estimated_hired = max(1, round(total_hired * lgbtq_percentage)) if total_hired > 0 else 0

        return jsonify({
            "applied": estimated_applied,
            "interviewed": estimated_interviewed,
            "hired": estimated_hired,
            "debug": {
                "total_applied": total_applied,
                "total_interviewed": total_interviewed,
                "total_hired": total_hired,
                "total_matched": total_matched,
                "status_breakdown": {
                    "hired": len([app for app in applications if app["status"] == "hired"]),
                    "matched": len([app for app in applications if app["status"] == "matched"]),
                    "pending": len([app for app in applications if app["status"] == "pending"]),
                    "dismissed": len([app for app in applications if app["status"] == "dismissed"])
                }
            }
        }), 200

    except Exception as e:
        return jsonify({
            "applied": 0,
            "interviewed": 0,
            "hired": 0,
            "error": str(e)
        }), 500

@dashboard_bp.route("/dashboard/candidates-by-location", methods=["GET"])
def candidates_by_location():
    supabase = current_app.supabase
    profiles = supabase.table("candidate_profiles").select("location").execute().data

    stats = defaultdict(int)
    for p in profiles:
        loc = p.get("location", "unknown")
        stats[loc] += 1

    return jsonify([{"location": k, "count": v} for k, v in stats.items()]), 200

@dashboard_bp.route("/dashboard/average-candidate-score", methods=["GET"])
def average_candidate_score():
    supabase = current_app.supabase
    profiles = supabase.table("candidate_profiles").select("skills_score", "context_score").execute().data

    total_skills = sum(p["skills_score"] or 0 for p in profiles)
    total_context = sum(p["context_score"] or 0 for p in profiles)
    count = len(profiles)

    return jsonify({
        "avgSkillsScore": round(total_skills / count, 2) if count else 0,
        "avgContextScore": round(total_context / count, 2) if count else 0
    }), 200

@dashboard_bp.route("/dashboard/jobs-by-company", methods=["GET"])
def jobs_by_company():
    supabase = current_app.supabase
    jobs = supabase.table("jobs").select("company_id").execute().data
    companies = supabase.table("companies").select("id", "name").execute().data

    company_map = {c["id"]: c["name"] for c in companies}
    stats = defaultdict(int)
    for job in jobs:
        name = company_map.get(job["company_id"], "unknown")
        stats[name] += 1

    return jsonify([{"company": k, "jobCount": v} for k, v in stats.items()]), 200

@dashboard_bp.route("/dashboard/top-skills", methods=["GET"])
def top_skills():
    supabase = current_app.supabase
    profiles = supabase.table("candidate_profiles").select("skills").execute().data

    skill_counts = defaultdict(int)
    for profile in profiles:
        skills = profile.get("skills", []) or []
        for skill in skills:
            skill_counts[skill.lower()] += 1

    sorted_skills = sorted(skill_counts.items(), key=lambda x: x[1], reverse=True)[:10]
    return jsonify([{"skill": s, "count": c} for s, c in sorted_skills]), 200

@dashboard_bp.route("/dashboard/gender-diversity", methods=["GET"])
def gender_diversity():
    supabase = current_app.supabase
    try:
        profiles = supabase.table("candidate_profiles").select("gender").execute().data
    except Exception as e:
        return jsonify({
            "error": "Failed to fetch gender data. Ensure 'gender' column exists.",
            "details": str(e)
        }), 500

    counts = defaultdict(int)
    for profile in profiles:
        gender = profile.get("gender", "unknown")
        counts[gender] += 1
    total = sum(counts.values())
    response = [
        {"gender": g, "count": c, "percentage": round((c / total) * 100)} for g, c in counts.items()
    ]
    return jsonify(response), 200

@dashboard_bp.route("/dashboard/recruiter-performance", methods=["GET"])
def recruiter_performance():
    supabase = current_app.supabase
    recruiters = supabase.table("recruiters").select("id", "full_name").execute().data
    companies = supabase.table("companies").select("id", "recruiter_id").execute().data
    jobs = supabase.table("jobs").select("id", "company_id").execute().data
    apps = supabase.table("applications").select("job_id", "status").execute().data

    recruiter_map = {r["id"]: r["full_name"] for r in recruiters}
    company_to_recruiter = {c["id"]: c["recruiter_id"] for c in companies}
    job_to_company = {j["id"]: j["company_id"] for j in jobs}

    performance = defaultdict(lambda: {"applied": 0, "hired": 0})

    for app in apps:
        job_id = app["job_id"]
        company_id = job_to_company.get(job_id)
        recruiter_id = company_to_recruiter.get(company_id)
        if recruiter_id:
            performance[recruiter_id]["applied"] += 1
            if app["status"] == "hired":
                performance[recruiter_id]["hired"] += 1

    response = [
        {"recruiter": recruiter_map.get(rid, "unknown"), **stats}
        for rid, stats in performance.items()
    ]
    return jsonify(response), 200

@dashboard_bp.route("/dashboard/time-to-hire", methods=["GET"])
def time_to_hire():
    supabase = current_app.supabase
    apps = supabase.table("applications").select("created_at", "status").execute().data

    hire_durations = []
    for app in apps:
        if app["status"] == "hired":
            try:
                created = datetime.strptime(app["created_at"], "%Y-%m-%dT%H:%M:%S.%f")
                days = (datetime.now() - created).days
                hire_durations.append(days)
            except:
                continue

    if not hire_durations:
        return jsonify({"averageDays": 0, "minDays": 0, "maxDays": 0}), 200

    return jsonify({
        "averageDays": round(sum(hire_durations) / len(hire_durations), 1),
        "minDays": min(hire_durations),
        "maxDays": max(hire_durations)
    }), 200

@dashboard_bp.route("/dashboard/application-funnel", methods=["GET"])
def application_funnel():
    supabase = current_app.supabase
    apps = supabase.table("applications").select("status").execute().data

    total = len(apps)
    counts = defaultdict(int)
    for app in apps:
        counts[app["status"]] += 1

    funnel = {
        "applied": counts.get("applied", 0),
        "shortlisted_round1": counts.get("shortlisted_round1", 0),
        "shortlisted_round2": counts.get("shortlisted_round2", 0),
        "hired": counts.get("hired", 0)
    }

    for key in funnel:
        funnel[key + "_percent"] = round((funnel[key] / total) * 100) if total else 0

    return jsonify(funnel), 200


@dashboard_bp.route("/dashboard/recruiter/job-popularity/<recruiter_id>", methods=["GET"])
def job_popularity_by_recruiter(recruiter_id):
    supabase = current_app.supabase

    # Récupérer les entreprises du recruteur
    companies = supabase.table("companies").select("id").eq("recruiter_id", recruiter_id).execute().data
    
    if not companies:
        return jsonify({"error": "No companies found for this recruiter."}), 404
    
    company_ids = [company["id"] for company in companies]

    # Récupérer les emplois associés à ces entreprises
    jobs = supabase.table("jobs").select("id", "company_id", "title").in_("company_id", company_ids).execute().data

    # Récupérer toutes les candidatures associées à ces emplois
    apps = supabase.table("applications").select("job_id").in_("job_id", [job["id"] for job in jobs]).execute().data

    # Compter les candidatures par emploi
    counts = defaultdict(int)
    for app in apps:
        job_id = app["job_id"]
        counts[job_id] += 1

    # Créer un dictionnaire avec les titres d'emplois pour chaque ID de job
    job_titles = {job["id"]: job["title"] for job in jobs}

    # Trier les emplois par nombre de candidatures (du plus populaire au moins populaire)
    sorted_jobs = [
        {"job": job_titles.get(job_id, "Unknown"), "applications": count}
        for job_id, count in sorted(counts.items(), key=lambda x: x[1], reverse=True)
    ]

    # Filtrer pour n'afficher que les emplois ayant plus de 5 candidatures (ou un autre seuil défini)
    min_applications = int(request.args.get('min_applications', 5))  # Par défaut, 5 candidatures minimum
    filtered_jobs = [job for job in sorted_jobs if job['applications'] >= min_applications]

    return jsonify(filtered_jobs), 200


@dashboard_bp.route("/dashboard/application-progression", methods=["GET"])#used
def get_application_progression():
    """
    Calcule la progression des candidatures de manière dynamique
    basée sur les statuts dans la table applications
    """
    try:
        supabase = current_app.supabase

        # Récupérer toutes les applications avec leurs statuts
        applications = supabase.table("applications").select("status").execute().data

        if not applications:
            # Retourner des valeurs par défaut si aucune donnée
            return jsonify({
                "applied": 0,
                "shortlisted_round1": 0,
                "shortlisted_round2": 0,
                "hired": 0
            }), 200

        # Compter les applications par statut
        status_counts = defaultdict(int)
        for app in applications:
            status = app.get("status", "pending")
            status_counts[status] += 1

        # Mapper les statuts aux étapes de progression
        # Vous pouvez ajuster ces mappings selon vos statuts réels
        result = {
            "applied": (
                status_counts.get("pending", 0) +
                status_counts.get("applied", 0) +
                status_counts.get("submitted", 0) +
                status_counts.get("received", 0)
            ),
            "shortlisted_round1": (
                status_counts.get("shortlisted", 0) +
                status_counts.get("shortlisted_round1", 0) +
                status_counts.get("screening", 0) +
                status_counts.get("reviewed", 0)
            ),
            "shortlisted_round2": (
                status_counts.get("shortlisted_round2", 0) +
                status_counts.get("interview_scheduled", 0) +
                status_counts.get("interviewed", 0) +
                status_counts.get("final_round", 0)
            ),
            "hired": (
                status_counts.get("hired", 0) +
                status_counts.get("accepted", 0) +
                status_counts.get("offer_accepted", 0)
            )
        }

        return jsonify(result), 200

    except Exception as e:
        print(f"Erreur lors du calcul de la progression des candidatures: {e}")
        # Retourner des valeurs par défaut en cas d'erreur
        return jsonify({
            "applied": 0,
            "shortlisted_round1": 0,
            "shortlisted_round2": 0,
            "hired": 0
        }), 200

@dashboard_bp.route("/offers/applications/by-recruiter/<recruiter_id>", methods=["GET"])
def get_offers_applications_by_recruiter(recruiter_id):
    supabase = current_app.supabase
    # 1. Get companies for this recruiter
    companies = supabase.table("companies").select("id").eq("recruiter_id", recruiter_id).execute().data
    company_ids = [c["id"] for c in companies]
    if not company_ids:
        return jsonify([])

    # 2. Get jobs for these companies
    jobs = supabase.table("jobs").select("id", "company_id", "title", "description", "location", "requirements", "education", "file_url", "created_at", "contract_type", "experience", "deadline", "salary").in_("company_id", company_ids).execute().data
    job_ids = [j["id"] for j in jobs]
    job_map = {j["id"]: j for j in jobs}
    if not job_ids:
        return jsonify([])

    # 3. Get applications for these jobs
    applications = supabase.table("applications").select("id", "job_id", "candidate_id", "status", "score", "created_at").in_("job_id", job_ids).execute().data

    # 4. Get candidate_job_matches for score and status from match_percentage and prediction
    candidate_job_matches = supabase.table("candidate_job_matches").select("candidate_id", "job_id", "match_percentage", "prediction").in_("job_id", job_ids).execute().data if job_ids else []
    # Create a mapping for quick lookup: (candidate_id, job_id) -> {match_percentage, prediction}
    match_map = {(match["candidate_id"], match["job_id"]): match for match in candidate_job_matches}

    # 5. Get all candidate_ids
    candidate_ids = list({a["candidate_id"] for a in applications if a["candidate_id"]}) if applications else []
    candidates = supabase.table("candidates").select("id", "email", "full_name", "phone", "cv_url", "created_at").in_("id", candidate_ids).execute().data if candidate_ids else []
    candidate_map = {c["id"]: c for c in candidates}
   # Récupération des profils avec des attributs supplémentaires
    profiles = (
    supabase.table("candidate_profiles")
    .select(
        "id", "candidate_id", "experience", "education", "skills", "cv_path",
        "skills_score", "context_score", "source", "location", "updated_at",
        "skillner_skills", "py_skills", "added_skills", "title", "about",
        "linkedin", "website", "github", "cv_last_updated", "certifications",
        "languages", "job_preferences"
    )
    .in_("candidate_id", candidate_ids)
    .execute()
    .data) if candidate_ids else []
    
    profile_map = {p["candidate_id"]: p for p in profiles}

    # 5. Group applications by job
    from collections import defaultdict
    apps_by_job = defaultdict(list)
    for app in applications:
        apps_by_job[app["job_id"]].append(app)

    offers = []
    for job_id, job in job_map.items():
        apps = apps_by_job.get(job_id, [])
        job_applications = []
        matched_count = 0
        for app in apps:
            candidate = candidate_map.get(app["candidate_id"], {})
            profile = profile_map.get(app["candidate_id"], {})

            # Get match data from candidate_job_matches table
            match_data = match_map.get((app["candidate_id"], app["job_id"]), {})

            # Use match_percentage as score from candidate_job_matches
            score = match_data.get("match_percentage", app.get("score"))  # Fallback to original score if no match data

            # Use status from applications table
            status = app["status"]

            # Use prediction from candidate_job_matches for matched field
            prediction = match_data.get("prediction", "")
            is_matched = prediction == "match"
            if is_matched:
                matched_count += 1
            job_applications.append({
                "application_id": app["id"],
                "appliedDate": app["created_at"],
                "candidate": candidate,
                "candidate_profile": profile,
                "score": score,
                "status": status,
                "matched": is_matched,
                "interviewDate": None
            })
        offer = OrderedDict([
            ("applicants", len(job_applications)),
            ("matched_applicants", matched_count),  # Nombre de candidats matched pour cette offre
            ("applications", job_applications),
            ("company_id", job.get("company_id")),
            ("contract_type", job.get("contract_type")),
            ("created_at", job.get("created_at")),
            ("deadline", job.get("deadline")),
            ("description", job.get("description")),
            ("education", job.get("education")),
            ("experience", job.get("experience")),
            ("file_url", job.get("file_url")),
            ("id", job_id),
            ("location", job.get("location")),
            ("matched", matched_count),
            ("requirements", job.get("requirements")),
            ("salary", job.get("salary")),
            ("title", job.get("title"))
        ])
        offers.append(offer)
    return jsonify(offers)